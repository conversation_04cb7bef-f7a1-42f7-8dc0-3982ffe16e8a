#!/usr/bin/env python3
"""
Comprehensive audio artifacts diagnostic tool
Identifies the exact source of small audio artifacts in the TTS pipeline
"""

import asyncio
import logging
import sys
import time
import wave
import numpy as np
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AudioArtifactDiagnostic:
    """Comprehensive audio artifact diagnostic tool"""
    
    def __init__(self):
        self.test_results = []
        self.audio_samples = {}
    
    def log_result(self, test_name: str, passed: bool, details: str = ""):
        """Log a diagnostic result"""
        status = "✅ CLEAN" if passed else "⚠️ ARTIFACTS"
        result = f"{status}: {test_name}"
        if details:
            result += f" - {details}"
        
        self.test_results.append(result)
        print(result)
    
    async def test_raw_orpheus_output(self):
        """Test raw Orpheus TTS output for artifacts"""
        print("\n🔍 Testing Raw Orpheus TTS Output")
        print("-" * 40)
        
        try:
            import aiohttp
            
            # Test direct API call to Orpheus
            api_config = {
                'url': 'https://model-4w7jnzyw.api.baseten.co/environments/production/predict',
                'api_key': 'k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw',
                'voice_id': 'tara'
            }
            
            payload = {
                'voice': 'tara',
                'prompt': 'Testing for audio artifacts in raw output.',
                'max_tokens': 200
            }
            
            headers = {"Authorization": f"Api-Key {api_config['api_key']}"}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    api_config['url'],
                    headers=headers,
                    json=payload,
                ) as resp:
                    if resp.status == 200:
                        audio_data = await resp.read()
                        
                        # Save raw output for analysis
                        with open("raw_orpheus_output.wav", "wb") as f:
                            f.write(audio_data)
                        
                        # Analyze for artifacts
                        artifacts_detected = self.analyze_audio_for_artifacts(audio_data)
                        self.log_result("Raw Orpheus Output", not artifacts_detected, 
                                      f"Saved to raw_orpheus_output.wav")
                        
                        self.audio_samples['raw_orpheus'] = audio_data
                    else:
                        self.log_result("Raw Orpheus Output", False, f"API error: {resp.status}")
        
        except Exception as e:
            self.log_result("Raw Orpheus Output", False, f"Error: {e}")
    
    async def test_tts_service_processing(self):
        """Test TTS service audio processing for artifacts"""
        print("\n🔧 Testing TTS Service Processing")
        print("-" * 40)
        
        try:
            from tts_service import convert_to_browser_compatible_audio
            
            # Use raw Orpheus output if available
            if 'raw_orpheus' in self.audio_samples:
                raw_audio = self.audio_samples['raw_orpheus']
                
                # Process through TTS service
                processed_audio, mime_type = convert_to_browser_compatible_audio(raw_audio)
                
                # Save processed output
                with open("processed_tts_service.wav", "wb") as f:
                    f.write(processed_audio)
                
                # Compare with raw
                artifacts_added = self.compare_audio_quality(raw_audio, processed_audio)
                self.log_result("TTS Service Processing", not artifacts_added,
                              f"Saved to processed_tts_service.wav")
                
                self.audio_samples['tts_service'] = processed_audio
            else:
                self.log_result("TTS Service Processing", False, "No raw audio to process")
        
        except Exception as e:
            self.log_result("TTS Service Processing", False, f"Error: {e}")
    
    async def test_livekit_audio_pipeline(self):
        """Test LiveKit audio pipeline for artifacts"""
        print("\n🎵 Testing LiveKit Audio Pipeline")
        print("-" * 40)
        
        try:
            from orpheus.tts import OrpheusTTS, Voice
            from livekit import rtc
            
            # Create TTS instance
            tts = OrpheusTTS(
                voice=Voice(id="tara", name="Tara"),
                chunk_size_bytes=4096,
            )
            
            # Test synthesis
            text = "Testing LiveKit audio pipeline for artifacts."
            synthesis_stream = tts.synthesize(text)
            
            # Collect audio frames
            audio_frames = []
            async for audio_event in synthesis_stream:
                if hasattr(audio_event, 'frame') and audio_event.frame:
                    audio_frames.append(audio_event.frame.data)
                    if len(audio_frames) >= 5:  # Get a few frames
                        break
            
            if audio_frames:
                # Combine frames
                combined_audio = b''.join(audio_frames)
                
                # Save LiveKit output
                with open("livekit_pipeline.wav", "wb") as f:
                    # Add simple WAV header
                    self.write_wav_file(f, combined_audio, 24000, 1, 16)
                
                # Analyze for artifacts
                artifacts_detected = self.analyze_audio_for_artifacts(combined_audio)
                self.log_result("LiveKit Pipeline", not artifacts_detected,
                              f"Saved to livekit_pipeline.wav, {len(audio_frames)} frames")
                
                self.audio_samples['livekit'] = combined_audio
            else:
                self.log_result("LiveKit Pipeline", False, "No audio frames received")
            
            await tts.aclose()
        
        except Exception as e:
            self.log_result("LiveKit Pipeline", False, f"Error: {e}")
    
    def analyze_audio_for_artifacts(self, audio_data: bytes) -> bool:
        """Analyze audio data for common artifacts"""
        try:
            # Skip WAV header if present
            if audio_data.startswith(b'RIFF'):
                # Find data chunk
                data_pos = audio_data.find(b'data')
                if data_pos != -1:
                    pcm_data = audio_data[data_pos + 8:]
                else:
                    pcm_data = audio_data[44:]  # Standard header size
            else:
                pcm_data = audio_data
            
            # Convert to numpy array
            audio_array = np.frombuffer(pcm_data, dtype=np.int16)
            
            if len(audio_array) == 0:
                return True  # No data = artifact
            
            # Check for common artifact patterns
            artifacts_found = []
            
            # 1. Check for sudden spikes (clicks/pops)
            diff = np.abs(np.diff(audio_array.astype(np.float32)))
            spike_threshold = np.std(diff) * 5
            spikes = np.where(diff > spike_threshold)[0]
            if len(spikes) > len(audio_array) * 0.001:  # More than 0.1% spikes
                artifacts_found.append(f"Audio spikes: {len(spikes)}")
            
            # 2. Check for DC offset
            dc_offset = np.mean(audio_array)
            if abs(dc_offset) > 100:
                artifacts_found.append(f"DC offset: {dc_offset:.1f}")
            
            # 3. Check for clipping
            max_val = np.max(np.abs(audio_array))
            if max_val >= 32767:
                artifacts_found.append("Audio clipping detected")
            
            # 4. Check for unusual frequency content (artifacts often show as high-freq noise)
            if len(audio_array) > 1024:
                fft = np.fft.fft(audio_array[:1024])
                high_freq_energy = np.sum(np.abs(fft[512:]))
                total_energy = np.sum(np.abs(fft))
                if total_energy > 0 and (high_freq_energy / total_energy) > 0.3:
                    artifacts_found.append("High frequency artifacts")
            
            if artifacts_found:
                print(f"   Artifacts detected: {', '.join(artifacts_found)}")
                return True
            
            return False
        
        except Exception as e:
            print(f"   Error analyzing audio: {e}")
            return True
    
    def compare_audio_quality(self, audio1: bytes, audio2: bytes) -> bool:
        """Compare two audio samples to see if quality degraded"""
        try:
            # Simple comparison - check if processing added artifacts
            artifacts1 = self.analyze_audio_for_artifacts(audio1)
            artifacts2 = self.analyze_audio_for_artifacts(audio2)
            
            # If second audio has artifacts but first doesn't, processing added them
            return artifacts2 and not artifacts1
        
        except Exception as e:
            print(f"   Error comparing audio: {e}")
            return True
    
    def write_wav_file(self, file, audio_data: bytes, sample_rate: int, channels: int, bits: int):
        """Write a simple WAV file"""
        import struct
        
        # WAV header
        file.write(b'RIFF')
        file.write(struct.pack('<I', 36 + len(audio_data)))
        file.write(b'WAVE')
        file.write(b'fmt ')
        file.write(struct.pack('<I', 16))
        file.write(struct.pack('<H', 1))  # PCM
        file.write(struct.pack('<H', channels))
        file.write(struct.pack('<I', sample_rate))
        file.write(struct.pack('<I', sample_rate * channels * bits // 8))
        file.write(struct.pack('<H', channels * bits // 8))
        file.write(struct.pack('<H', bits))
        file.write(b'data')
        file.write(struct.pack('<I', len(audio_data)))
        file.write(audio_data)
    
    async def test_browser_audio_context(self):
        """Test browser audio context settings"""
        print("\n🌐 Testing Browser Audio Context")
        print("-" * 40)
        
        # This would need to be run in browser, but we can check settings
        print("   Browser audio context should be configured for:")
        print("   - Sample rate: 24000 Hz")
        print("   - Echo cancellation: disabled")
        print("   - Noise suppression: disabled")
        print("   - Auto gain control: disabled")
        
        self.log_result("Browser Audio Context", True, "Settings documented")
    
    def print_summary(self):
        """Print diagnostic summary"""
        print("\n" + "=" * 60)
        print("🔍 AUDIO ARTIFACTS DIAGNOSTIC SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            print(result)
        
        print(f"\n📁 Audio samples saved:")
        for name, data in self.audio_samples.items():
            print(f"   - {name}: {len(data)} bytes")
        
        print(f"\n💡 Recommendations:")
        print("   1. Compare audio files manually for quality differences")
        print("   2. Check browser developer tools for audio processing warnings")
        print("   3. Test with different browsers to isolate browser-specific issues")
        print("   4. Monitor network conditions during calls")
        print("   5. Check LiveKit server logs for audio processing errors")

async def main():
    """Main diagnostic function"""
    print("🔍 AUDIO ARTIFACTS DIAGNOSTIC TOOL")
    print("This tool will test each component in the audio pipeline")
    print("=" * 60)
    
    diagnostic = AudioArtifactDiagnostic()
    
    # Run all diagnostic tests
    await diagnostic.test_raw_orpheus_output()
    await diagnostic.test_tts_service_processing()
    await diagnostic.test_livekit_audio_pipeline()
    await diagnostic.test_browser_audio_context()
    
    # Print summary
    diagnostic.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
