/**
 * Audio optimization utilities to prevent click sounds and audio artifacts
 * during initialization and playback in web applications.
 *
 * CRITICAL FIX: Handles browser audio context mode switching that causes artifacts
 * when transitioning from media playback to WebRTC communications mode.
 */

// Global audio context management
let globalAudioContext: AudioContext | null = null;
let isWebRTCActive = false;

/**
 * CRITICAL FIX: Pre-initialize audio context to prevent mode switching artifacts
 * This must be called BEFORE any WebRTC connection to establish the correct audio mode
 */
export const preInitializeAudioForWebRTC = async (): Promise<void> => {
  try {
    console.log('🔧 Pre-initializing audio context for WebRTC to prevent mode switching artifacts');

    // Create a temporary audio context to "claim" the audio system
    // This prevents the browser from switching modes when WebRTC starts
    const tempContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      latencyHint: 'interactive',  // Optimized for real-time communication
      sampleRate: 48000  // CRITICAL: Use 48kHz to match WebRTC standard and prevent resampling
    });

    // Ensure context is active
    if (tempContext.state === 'suspended') {
      await tempContext.resume();
    }

    // Create a very brief silent tone to activate the audio pipeline
    // This "warms up" the audio system in the correct mode
    const buffer = tempContext.createBuffer(1, 480, 48000); // 10ms at 48kHz
    const source = tempContext.createBufferSource();
    const gainNode = tempContext.createGain();

    // Set very low volume to avoid any audible sound
    gainNode.gain.setValueAtTime(0.001, tempContext.currentTime);

    source.buffer = buffer;
    source.connect(gainNode);
    gainNode.connect(tempContext.destination);
    source.start();

    // Wait for the audio system to stabilize
    await new Promise(resolve => setTimeout(resolve, 50));

    // Keep this context as our global context
    globalAudioContext = tempContext;
    isWebRTCActive = true;

    console.log('✅ Audio context pre-initialized for WebRTC at 48kHz');

  } catch (error) {
    console.error('❌ Failed to pre-initialize audio context:', error);
    throw error;
  }
};

/**
 * Initialize the global audio context smoothly to prevent click sounds
 */
export const initializeAudioContext = async (): Promise<AudioContext> => {
  if (globalAudioContext && globalAudioContext.state !== 'closed') {
    return globalAudioContext;
  }

  try {
    // CRITICAL FIX: Use 48kHz for WebRTC compatibility and to prevent browser mode switching
    globalAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      latencyHint: 'interactive',
      sampleRate: 48000  // FIXED: Use 48kHz to match WebRTC and prevent resampling artifacts
    });

    // Ensure context is running
    if (globalAudioContext.state === 'suspended') {
      await globalAudioContext.resume();
    }

    // Create a brief silent buffer to "warm up" the audio system
    const buffer = globalAudioContext.createBuffer(1, 1, globalAudioContext.sampleRate);
    const source = globalAudioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(globalAudioContext.destination);
    source.start();

    console.log('🎵 Audio context initialized smoothly at 48kHz');
    return globalAudioContext;
  } catch (error) {
    console.error('Failed to initialize audio context:', error);
    throw error;
  }
};

// Volume ramping function removed per user request - no longer manipulating audio volume

/**
 * Smooth audio element initialization to prevent click sounds
 */
export const initializeAudioElementSmoothly = async (audioElement: HTMLAudioElement): Promise<() => void> => {
  // Ensure audio context is initialized
  await initializeAudioContext();
  
  // Set initial properties
  audioElement.volume = 0;
  audioElement.preload = 'none';
  
  // Add event listeners for basic playback
  const handlePlay = () => {
    console.log('Audio started playing');
  };
  
  const handlePause = () => {
    // Smooth fade out when pausing
    const currentVolume = audioElement.volume;
    const fadeOut = (volume: number) => {
      if (volume > 0 && !audioElement.paused) {
        audioElement.volume = Math.max(0, volume - 0.1);
        setTimeout(() => fadeOut(audioElement.volume), 10);
      }
    };
    fadeOut(currentVolume);
  };
  
  audioElement.addEventListener('play', handlePlay);
  audioElement.addEventListener('pause', handlePause);
  
  // Return cleanup function
  return () => {
    audioElement.removeEventListener('play', handlePlay);
    audioElement.removeEventListener('pause', handlePause);
  };
};

/**
 * CRITICAL FIX: Optimize WebRTC connection to prevent audio mode switching artifacts
 */
export const optimizeWebRTCConnection = async (connectionFunction: () => Promise<void>): Promise<void> => {
  try {
    console.log('🔧 Starting optimized WebRTC connection to prevent audio artifacts');

    // Step 1: Pre-initialize audio context in WebRTC mode
    await preInitializeAudioForWebRTC();

    // Step 2: Brief delay to allow audio system to stabilize in the correct mode
    await new Promise(resolve => setTimeout(resolve, 100));

    // Step 3: Execute the WebRTC connection
    await connectionFunction();

    // Step 4: Additional stabilization delay
    await new Promise(resolve => setTimeout(resolve, 150));

    console.log('✅ WebRTC connection optimized - audio artifacts should be prevented');

  } catch (error) {
    console.error('❌ Error during optimized WebRTC connection:', error);
    throw error;
  }
};

/**
 * Optimize microphone activation to prevent click sounds
 */
export const optimizeMicrophoneActivation = async (micActivationFunction: () => Promise<void>): Promise<void> => {
  try {
    // Initialize audio context first (will use existing WebRTC context if available)
    await initializeAudioContext();

    // Brief delay to allow audio system to stabilize
    await new Promise(resolve => setTimeout(resolve, 50));

    // Activate microphone
    await micActivationFunction();

    // Additional delay for microphone to fully initialize
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log('🎤 Microphone activated with click prevention');
  } catch (error) {
    console.error('Error during optimized microphone activation:', error);
    throw error;
  }
};

/**
 * CRITICAL FIX: Clean up audio context when disconnecting to prevent lingering artifacts
 */
export const cleanupAudioContext = async (): Promise<void> => {
  try {
    if (globalAudioContext && globalAudioContext.state !== 'closed') {
      console.log('🧹 Cleaning up audio context to prevent lingering artifacts');

      // Gradually fade out any remaining audio
      const gainNode = globalAudioContext.createGain();
      gainNode.gain.setValueAtTime(1, globalAudioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.001, globalAudioContext.currentTime + 0.1);

      // Wait for fade out
      await new Promise(resolve => setTimeout(resolve, 150));

      // Close the context
      await globalAudioContext.close();
      globalAudioContext = null;
      isWebRTCActive = false;

      console.log('✅ Audio context cleaned up successfully');
    }
  } catch (error) {
    console.error('❌ Error cleaning up audio context:', error);
  }
};

/**
 * Preload and optimize audio for smooth playback
 */
export const preloadAudioSmoothly = async (audioUrl: string): Promise<HTMLAudioElement> => {
  const audio = new Audio();
  
  // Initialize smoothly
  await initializeAudioElementSmoothly(audio);
  
  return new Promise((resolve, reject) => {
    const handleCanPlay = () => {
      audio.removeEventListener('canplaythrough', handleCanPlay);
      audio.removeEventListener('error', handleError);
      resolve(audio);
    };
    
    const handleError = (e: ErrorEvent) => {
      audio.removeEventListener('canplaythrough', handleCanPlay);
      audio.removeEventListener('error', handleError);
      reject(e);
    };
    
    audio.addEventListener('canplaythrough', handleCanPlay);
    audio.addEventListener('error', handleError);
    
    audio.src = audioUrl;
    audio.load();
  });
};

export default {
  initializeAudioContext,
  initializeAudioElementSmoothly,
  optimizeMicrophoneActivation,
  preloadAudioSmoothly
}; 