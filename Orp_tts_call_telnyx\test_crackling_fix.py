#!/usr/bin/env python3
"""
Quick test to verify the loose cable crackling fix works.
"""

import numpy as np
import logging
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_crackling_fix():
    """Test the prevent_loose_cable_crackling function."""
    logger.info("🧪 Testing loose cable crackling fix")
    
    try:
        from orpheus.tts import prevent_loose_cable_crackling
        
        # Create test audio with potential crackling issues
        sample_rate = 48000
        duration = 0.1  # 100ms
        samples = int(sample_rate * duration)
        
        # Generate test audio with abrupt start/end (would cause crackling)
        t = np.linspace(0, duration, samples, False)
        audio = np.sin(2 * np.pi * 440 * t) * 16000  # 440Hz tone at high volume
        audio_samples = audio.astype(np.int16)
        
        # Make it start/end abruptly (crackling-prone)
        audio_samples[0] = 16000  # Abrupt start
        audio_samples[-1] = -16000  # Abrupt end
        
        logger.info(f"📊 Original audio: first={audio_samples[0]}, last={audio_samples[-1]}")
        
        # Convert to bytes and apply fix
        audio_bytes = audio_samples.tobytes()
        fixed_bytes = prevent_loose_cable_crackling(audio_bytes, sample_rate=48000)
        
        # Convert back and check
        fixed_samples = np.frombuffer(fixed_bytes, dtype=np.int16)
        
        logger.info(f"📊 Fixed audio: first={fixed_samples[0]}, last={fixed_samples[-1]}")
        
        # Check if the abrupt start/end was smoothed
        start_reduced = abs(fixed_samples[0]) < abs(audio_samples[0])
        end_reduced = abs(fixed_samples[-1]) < abs(audio_samples[-1])
        
        if start_reduced and end_reduced:
            logger.info("✅ Crackling fix test PASSED - abrupt start/end smoothed")
            return True
        else:
            logger.error("❌ Crackling fix test FAILED - start/end not smoothed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Crackling fix test failed: {e}")
        return False

def test_buffer_continuity():
    """Test the buffer continuity fix."""
    logger.info("🧪 Testing buffer continuity fix")
    
    try:
        # Simulate the buffer continuity function
        def test_buffer_continuity_func(audio_data: bytes, last_sample: int = 0):
            samples = np.frombuffer(audio_data, dtype=np.int16).copy()
            
            if len(samples) > 0:
                first_sample = samples[0]
                sample_diff = abs(int(first_sample) - int(last_sample))
                
                if sample_diff > 1000:  # Discontinuity threshold
                    ramp_length = min(10, len(samples))
                    ramp = np.linspace(last_sample, first_sample, ramp_length)
                    samples[:ramp_length] = ramp.astype(np.int16)
                    logger.info(f"🔧 Smoothed discontinuity: {last_sample} -> {first_sample}")
                
                return samples.tobytes(), int(samples[-1])
            
            return audio_data, last_sample
        
        # Create two audio segments with large discontinuity
        segment1 = np.array([100, 200, 300, 400, 500], dtype=np.int16)
        segment2 = np.array([15000, 14000, 13000, 12000, 11000], dtype=np.int16)  # Large jump
        
        logger.info(f"📊 Segment 1 end: {segment1[-1]}, Segment 2 start: {segment2[0]}")
        logger.info(f"📊 Discontinuity size: {abs(segment2[0] - segment1[-1])}")
        
        # Process segments
        _, last_sample = test_buffer_continuity_func(segment1.tobytes(), 0)
        fixed_bytes, _ = test_buffer_continuity_func(segment2.tobytes(), last_sample)
        fixed_segment2 = np.frombuffer(fixed_bytes, dtype=np.int16)
        
        logger.info(f"📊 Fixed segment 2 start: {fixed_segment2[0]}")
        
        # Check if discontinuity was reduced
        original_jump = abs(segment2[0] - segment1[-1])
        fixed_jump = abs(fixed_segment2[0] - segment1[-1])
        
        if fixed_jump < original_jump:
            logger.info(f"✅ Buffer continuity test PASSED - jump reduced from {original_jump} to {fixed_jump}")
            return True
        else:
            logger.error(f"❌ Buffer continuity test FAILED - jump not reduced")
            return False
            
    except Exception as e:
        logger.error(f"❌ Buffer continuity test failed: {e}")
        return False

def main():
    """Run the crackling fix tests."""
    logger.info("🚀 Testing loose cable crackling fixes")
    logger.info("=" * 50)
    
    tests = [
        ("Anti-Crackling Processing", test_crackling_fix),
        ("Buffer Continuity", test_buffer_continuity),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*30}")
        logger.info(f"Running {test_name}")
        logger.info(f"{'='*30}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*30}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*30}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 Crackling fixes are working!")
        logger.info("The loose cable artifacts should be eliminated.")
    else:
        logger.error("💥 Some fixes failed.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
