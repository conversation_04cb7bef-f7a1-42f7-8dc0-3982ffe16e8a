"use client";

// API URL configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090';

import { NoAgentNotification } from "./NoAgentNotification";
import {
  BarVisualizer,
  Disconnect<PERSON>utton,
  RoomAudioRenderer,
  RoomContext,
  VideoTrack,
  VoiceAssistantControlBar,
  useVoiceAssistant,
} from "@livekit/components-react";
import { AnimatePresence, motion } from "framer-motion";
import { Room, RoomEvent } from "livekit-client";
import { useCallback, useEffect, useState } from "react";
import { Mic, Phone, PhoneOff, Loader2, Volume2 } from "lucide-react";
import { optimizeWebRTCConnection, cleanupAudioContext } from '../utils/audioOptimization';

interface ConnectionDetails {
  serverUrl: string;
  participantToken: string;
  roomName: string;
  participantIdentity: string;
  agentId: string;
  userId: string;
}

interface WebCallingInterfaceProps {
  agentId: string;
  agentName: string;
  userId?: string;
  onEndCall?: () => void;
}

export function WebCallingInterface({ agentId, agentName, userId, onEndCall }: WebCallingInterfaceProps) {
  const [room] = useState(new Room());
  const [isSpawningWorker, setIsSpawningWorker] = useState(false);
  const [workerError, setWorkerError] = useState("");

  const onConnectButtonClicked = useCallback(async () => {
    try {
      setIsSpawningWorker(true);
      setWorkerError("");

      // CRITICAL FIX: Use optimized WebRTC connection to prevent audio mode switching artifacts
      await optimizeWebRTCConnection(async () => {
        // Step 1: Request microphone permissions with audio processing disabled
        console.log("🎤 Requesting microphone permissions...");
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: false,  // FIXED: Disable echo cancellation to prevent artifacts
              noiseSuppression: false,  // FIXED: Disable noise suppression to prevent artifacts
              autoGainControl: false,   // FIXED: Disable auto gain control to prevent artifacts
              sampleRate: 48000,        // CRITICAL FIX: Use 48kHz to match WebRTC standard and prevent resampling
              channelCount: 1           // FIXED: Mono audio
            },
            video: false
          });

          // Stop the stream immediately - we just needed to check permissions
          stream.getTracks().forEach(track => track.stop());
          console.log("✅ Microphone permissions granted with optimized audio settings");
        } catch (permissionError) {
          console.error("❌ Microphone permission denied:", permissionError);
          throw new Error("Microphone permission is required for voice calling. Please allow microphone access and try again.");
        }

        // Step 2: Get connection details first to generate unique room name
        const url = new URL(`${API_URL}/api/connection-details`);

        // Add agent ID and user ID to the connection details request for room isolation
        if (agentId) {
          url.searchParams.set('agentId', agentId);
        }
        if (userId) {
          url.searchParams.set('userId', userId);
        }

        const response = await fetch(url.toString());

        console.log('🔗 Connection details response status:', response.status);

        if (!response.ok) {
          const errorData = await response.text();
          console.error('🔗 Connection details error:', errorData);
          throw new Error(`Failed to get connection details: ${response.status} - ${errorData}`);
        }

        const connectionDetailsData: ConnectionDetails = await response.json();
        console.log('🔗 Connection details received:', connectionDetailsData);

        // Step 3: Spawn LiveKit worker for the specific room (FIXED: now includes room_name)
        if (agentId) {
          console.log("🤖 Spawning LiveKit worker for agent ID:", agentId, "in room:", connectionDetailsData.roomName);

          const workerResponse = await fetch(`${API_URL}/api/livekit/spawn`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              agent_id: agentId,
              room_name: connectionDetailsData.roomName,  // 🔧 FIX: Pass the unique room name
            }),
          });

          if (!workerResponse.ok) {
            const errorData = await workerResponse.json();
            throw new Error(errorData.error || 'LiveKit worker not available for this agent');
          }

          const workerResult = await workerResponse.json();
          console.log('✅ LiveKit worker spawned successfully:', workerResult);
        }

        // Step 4: Connect to LiveKit room and enable microphone with optimized audio settings
        await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);
        await room.localParticipant.setMicrophoneEnabled(true, {
          echoCancellation: false,  // FIXED: Disable echo cancellation to prevent artifacts
          noiseSuppression: false,  // FIXED: Disable noise suppression to prevent artifacts
          autoGainControl: false,   // FIXED: Disable auto gain control to prevent artifacts
          sampleRate: 48000,        // CRITICAL FIX: Use 48kHz to match WebRTC standard
          channelCount: 1           // FIXED: Mono audio
        });

        console.log("🎉 Successfully connected to voice assistant with optimized audio");
      });
    } catch (error) {
      console.error('🚨 Error connecting to voice assistant:', error);
      setWorkerError(error instanceof Error ? error.message : 'Failed to connect');
    } finally {
      setIsSpawningWorker(false);
    }
  }, [room, agentId]);

  useEffect(() => {
    room.on(RoomEvent.MediaDevicesError, onDeviceFailure);

    return () => {
      room.off(RoomEvent.MediaDevicesError, onDeviceFailure);
      // CRITICAL FIX: Clean up audio context when component unmounts
      cleanupAudioContext().catch(console.error);
    };
  }, [room]);

  function onDeviceFailure(error: Error) {
    console.error("Device failure:", error);
    
    // More specific error message based on the error type
    let errorMessage = "Microphone access error. ";
    
    if (error.name === "NotAllowedError") {
      errorMessage += "Please allow microphone access in your browser settings and refresh the page.";
    } else if (error.name === "NotFoundError") {
      errorMessage += "No microphone found. Please connect a microphone and try again.";
    } else if (error.name === "NotReadableError") {
      errorMessage += "Microphone is being used by another application. Please close other apps using your microphone.";
    } else {
      errorMessage += "Please check your microphone settings and refresh the page.";
    }
    
    setWorkerError(errorMessage);
  }

  return (
    <div className="w-full h-full bg-white flex flex-col overflow-hidden">
      <RoomContext.Provider value={room}>
        <SimpleVoiceAssistant 
          onConnectButtonClicked={onConnectButtonClicked}
          isSpawningWorker={isSpawningWorker}
          workerError={workerError}
          agentName={agentName}
          onEndCall={onEndCall}
        />
      </RoomContext.Provider>
    </div>
  );
}

function SimpleVoiceAssistant(props: { 
  onConnectButtonClicked: () => void;
  isSpawningWorker: boolean;
  workerError: string;
  agentName: string;
  onEndCall?: () => void;
}) {
  const { state: agentState } = useVoiceAssistant();

  return (
    <div className="w-full h-full min-h-0 flex flex-col">
      <AnimatePresence mode="wait">
        {agentState === "disconnected" ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col items-center justify-center flex-1 p-3 sm:p-4 lg:p-6"
          >
            {/* Responsive Header */}
            <div className="text-center mb-4 sm:mb-6 lg:mb-8">
              <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Phone className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 text-white" />
              </div>
              <h3 className="text-sm sm:text-lg lg:text-xl font-semibold text-gray-900 mb-1 sm:mb-2">
                {props.agentName}
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 mb-2">Ready to start conversation</p>
              
              {/* Microphone permission notice */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 sm:p-3 mx-auto max-w-xs sm:max-w-md">
                <div className="flex items-center justify-center gap-1 sm:gap-2 text-blue-700">
                  <Mic className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="text-xs sm:text-sm">Microphone access required</span>
                </div>
              </div>
            </div>

            {/* Responsive Error Display */}
              {props.workerError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-2 sm:p-3 mb-3 sm:mb-4 w-full max-w-xs sm:max-w-md text-center">
                <p className="text-red-700 text-xs sm:text-sm">{props.workerError}</p>
              </div>
              )}

            {/* Responsive Call Button */}
            <button
              className="flex items-center justify-center gap-2 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm lg:text-base w-full max-w-xs sm:max-w-sm"
                onClick={() => props.onConnectButtonClicked()}
                disabled={props.isSpawningWorker}
              >
                  {props.isSpawningWorker ? (
                    <>
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                  <span className="hidden xs:inline sm:hidden lg:inline">Connecting...</span>
                  <span className="xs:hidden sm:inline lg:hidden">Connecting</span>
                    </>
                  ) : (
                    <>
                  <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden xs:inline sm:hidden lg:inline">Start Conversation</span>
                  <span className="xs:hidden sm:inline lg:hidden">Call</span>
                    </>
                  )}
            </button>
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col w-full h-full min-h-0"
          >
            {/* Responsive Header Bar */}
            <div className="flex items-center justify-between p-2 sm:p-3 lg:p-4 bg-white border-b border-gray-200 flex-shrink-0 shadow-sm">
              {/* Agent Info - Responsive */}
              <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Phone className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900 text-xs sm:text-sm lg:text-base truncate">
                    {props.agentName}
                  </h3>
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-gray-600 hidden sm:inline">Connected</span>
                  </div>
                </div>
              </div>

              {/* Agent State - Responsive */}
              <div className="mx-2 sm:mx-3">
                <ResponsiveAgentStateIndicator state={agentState} />
                </div>

              {/* Controls - Responsive */}
              <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                <div className="hidden sm:block [&_.lk-button]:bg-gray-50 [&_.lk-button]:hover:bg-gray-100 [&_.lk-button]:border-gray-200 [&_.lk-button]:text-gray-700 [&_.lk-button]:shadow-sm [&_.lk-button]:w-7 [&_.lk-button]:h-7 [&_.lk-button]:rounded-md [&_.lk-button]:transition-all">
                    <VoiceAssistantControlBar controls={{ leave: false }} />
                  </div>
                    <DisconnectButton
                  className="bg-red-500 hover:bg-red-600 text-white rounded-md w-7 h-7 sm:w-8 sm:h-8 shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center"
                      onClick={props.onEndCall}
                    >
                  <PhoneOff className="w-3 h-3 sm:w-4 sm:h-4" />
                    </DisconnectButton>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 min-h-0 bg-white flex items-center justify-center">
              <div className="text-center p-4">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Phone className="w-8 h-8 sm:w-10 sm:h-10 text-green-600" />
              </div>
                <h3 className="text-sm sm:text-base font-medium text-gray-900 mb-2">
                  Connected to {props.agentName}
                </h3>
                <p className="text-xs sm:text-sm text-gray-600">
                  Voice conversation is active
                </p>
              </div>
            </div>

            <RoomAudioRenderer />
            <NoAgentNotification state={agentState} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Responsive Agent State Indicator
function ResponsiveAgentStateIndicator({ state }: { state: string }) {
  const getStateInfo = () => {
    switch (state) {
      case "listening":
        return { text: "Listening", shortText: "Mic", color: "bg-blue-500", icon: Mic };
      case "thinking":
        return { text: "Thinking", shortText: "AI", color: "bg-yellow-500", icon: Loader2 };
      case "speaking":
        return { text: "Speaking", shortText: "Talk", color: "bg-green-500", icon: Volume2 };
      default:
        return { text: "Ready", shortText: "Ready", color: "bg-gray-400", icon: Mic };
    }
  };

  const stateInfo = getStateInfo();

    return (
    <div className="flex items-center gap-1 sm:gap-2 px-1.5 sm:px-2 py-1 bg-gray-50 border border-gray-200 rounded-md">
      <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full ${stateInfo.color} ${state === 'thinking' ? 'animate-pulse' : ''}`}></div>
      {/* Show full text on larger screens, short text on mobile */}
      <span className="text-xs text-gray-600 hidden sm:inline">{stateInfo.text}</span>
      <span className="text-xs text-gray-600 sm:hidden">{stateInfo.shortText}</span>
      </div>
  );
}