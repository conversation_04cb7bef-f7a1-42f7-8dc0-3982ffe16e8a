"""
Optimized Orpheus TTS Implementation for LiveKit Agents
Fixes audio artifacts and word cutting issues through proper sample rate handling and text preprocessing.
"""

import asyncio
import logging
import uuid
import numpy as np
from typing import Optional
import aiohttp

from livekit import rtc
from livekit.agents import tts, utils
from livekit.agents.tts import AudioEmitter, SynthesizedAudio

# Audio processing imports
try:
    import scipy.signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

logger = logging.getLogger(__name__)

class OptimizedOrpheusTTS(tts.TTS):
    """
    Optimized Orpheus TTS implementation that fixes audio artifacts and word cutting.
    
    Key improvements:
    1. Proper 24kHz -> 48kHz resampling to fix LiveKit compatibility
    2. Enhanced text preprocessing to prevent word cutting
    3. Silence padding to prevent audio truncation
    4. Minimal audio processing to avoid artifacts
    """
    
    def __init__(
        self,
        *,
        voice: str = "tara",
        api_key: str = "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        enable_resampling: bool = True,
        add_silence_padding: bool = True,
        http_session: Optional[aiohttp.ClientSession] = None,
    ):
        # Voice configurations
        self._voice_configs = {
            "tara": {
                "api_url": "https://model-4w7jnzyw.api.baseten.co/environments/production/predict",
                "api_voice_name": "tara"
            },
            "elise": {
                "api_url": "https://model-5qenjjpq.api.baseten.co/environments/production/predict", 
                "api_voice_name": "elise"
            }
        }
        
        self._voice = voice.lower()
        self._api_key = api_key
        self._enable_resampling = enable_resampling
        self._add_silence_padding = add_silence_padding
        self._session = http_session
        
        # Get voice configuration
        if self._voice not in self._voice_configs:
            logger.warning(f"Voice '{voice}' not found, defaulting to 'tara'")
            self._voice = "tara"
        
        self._voice_config = self._voice_configs[self._voice]
        
        # Initialize with LiveKit-compatible settings
        super().__init__(
            capabilities=tts.TTSCapabilities(streaming=False),  # Use non-streaming for stability
            sample_rate=48000,  # LiveKit-compatible sample rate
            num_channels=1,
        )
        
        logger.info(f"🎤 Initialized OptimizedOrpheusTTS with voice: {voice}")
        logger.info(f"🔄 Resampling enabled: {enable_resampling}")
        logger.info(f"🔇 Silence padding enabled: {add_silence_padding}")

    def _preprocess_text(self, text: str) -> str:
        """Enhanced text preprocessing to prevent word cutting."""
        if not text or not text.strip():
            return "Hello, this is a test message."
        
        text = text.strip()
        
        # Ensure proper capitalization
        if text and not text[0].isupper():
            text = text[0].upper() + text[1:]
        
        # Add strategic context based on text length
        word_count = len(text.split())
        
        if word_count <= 2:
            # Very short - add substantial context
            enhanced_text = f"Let me say this clearly: {text}. That's the message."
        elif word_count <= 5:
            # Short - add moderate context
            enhanced_text = f"Here's what I want to tell you: {text}. Got it?"
        elif word_count <= 10:
            # Medium - add minimal context
            enhanced_text = f"Okay, {text}. That's what I meant."
        else:
            # Long - minimal intervention
            enhanced_text = text
        
        # Ensure proper punctuation
        if not enhanced_text.endswith(('.', '!', '?')):
            enhanced_text += '.'
        
        # Add breathing room for synthesis stability
        final_text = f"... {enhanced_text} ..."
        
        logger.info(f"📝 Text preprocessing: '{text}' -> '{final_text}'")
        return final_text

    def _resample_audio(self, audio_data: bytes) -> bytes:
        """High-quality resampling from 24kHz to 48kHz."""
        if not self._enable_resampling:
            return audio_data
            
        try:
            if not SCIPY_AVAILABLE:
                # Simple 2x upsampling fallback
                samples = np.frombuffer(audio_data, dtype=np.int16)
                upsampled = np.repeat(samples, 2)
                logger.debug("🔄 Applied basic 2x upsampling")
                return upsampled.tobytes()
            
            # High-quality resampling using scipy
            samples = np.frombuffer(audio_data, dtype=np.int16)
            resampled = scipy.signal.resample(samples, len(samples) * 2)
            resampled = np.clip(resampled, -32768, 32767).astype(np.int16)
            
            logger.debug(f"🔄 High-quality resampling: {len(samples)} -> {len(resampled)} samples")
            return resampled.tobytes()
            
        except Exception as e:
            logger.error(f"❌ Resampling failed: {e}")
            return audio_data

    def _add_silence_padding(self, audio_data: bytes, padding_ms: int = 100) -> bytes:
        """Add silence padding to prevent word cutting."""
        if not self._add_silence_padding:
            return audio_data
            
        try:
            # Calculate silence samples (48kHz, 16-bit)
            silence_samples = int((padding_ms / 1000.0) * 48000)
            silence_bytes = b'\x00\x00' * silence_samples
            
            # Add padding at start and end
            padded_audio = silence_bytes + audio_data + silence_bytes
            
            logger.debug(f"🔇 Added {padding_ms}ms silence padding")
            return padded_audio
            
        except Exception as e:
            logger.error(f"❌ Silence padding failed: {e}")
            return audio_data

    async def _ensure_session(self) -> aiohttp.ClientSession:
        """Ensure we have a valid HTTP session."""
        if not self._session or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=60.0)
            self._session = aiohttp.ClientSession(timeout=timeout)
        return self._session

    def synthesize(self, text: str, **kwargs) -> "OptimizedSynthesizeStream":
        """Create a synthesis stream for the given text."""
        return OptimizedSynthesizeStream(
            tts=self,
            input_text=text,
        )

class OptimizedSynthesizeStream(tts.SynthesizeStream):
    """Optimized synthesis stream that handles the actual TTS generation."""
    
    def __init__(self, *, tts: OptimizedOrpheusTTS, input_text: str):
        super().__init__(tts=tts, input_text=input_text)
        self._tts = tts

    async def _run(self, output_emitter: AudioEmitter) -> None:
        """Run the synthesis with optimized audio processing."""
        # Preprocess text to prevent word cutting
        processed_text = self._tts._preprocess_text(self.input_text)
        
        # Get session and prepare request
        session = await self._tts._ensure_session()
        request_id = str(uuid.uuid4())
        
        # Initialize output emitter
        output_emitter.initialize(
            request_id=request_id,
            sample_rate=48000,
            num_channels=1,
            mime_type="audio/pcm",
        )
        
        # Prepare API request
        payload = {
            'voice': self._tts._voice_config["api_voice_name"],
            'prompt': processed_text,
            'max_tokens': 10000
        }
        
        headers = {
            "Authorization": f"Api-Key {self._tts._api_key}",
            "Content-Type": "application/json"
        }
        
        logger.info(f"🌐 Synthesizing with voice: {self._tts._voice}")
        logger.info(f"📝 Text: {processed_text[:100]}...")
        
        try:
            async with session.post(
                self._tts._voice_config["api_url"],
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60.0)
            ) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"❌ API error: {resp.status} - {error_text}")
                    return
                
                # Collect all audio data
                accumulated_audio = b''
                async for chunk in resp.content.iter_chunked(8192):
                    if chunk:
                        # Skip WAV header on first chunk
                        if not accumulated_audio and chunk.startswith(b'RIFF'):
                            data_pos = chunk.find(b'data')
                            if data_pos != -1:
                                chunk = chunk[data_pos + 8:]
                            else:
                                chunk = chunk[44:] if len(chunk) > 44 else chunk
                        
                        accumulated_audio += chunk
                
                if accumulated_audio:
                    # Apply optimizations
                    processed_audio = self._tts._resample_audio(accumulated_audio)
                    processed_audio = self._tts._add_silence_padding(processed_audio)
                    
                    # Send to output
                    output_emitter.push(processed_audio)
                    output_emitter.flush()
                    
                    logger.info(f"✅ Synthesis complete: {len(accumulated_audio)} -> {len(processed_audio)} bytes")
                
        except Exception as e:
            logger.error(f"❌ Synthesis failed: {e}")
            raise
