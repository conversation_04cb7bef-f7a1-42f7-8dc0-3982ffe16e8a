#!/usr/bin/env python3
"""
Test script for the optimized Orpheus TTS implementation.
This script tests the fixes for audio artifacts and word cutting.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from orpheus_optimized_tts import OptimizedOrpheusTTS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_optimized_tts():
    """Test the optimized TTS implementation."""
    logger.info("🧪 Starting OptimizedOrpheusTTS test")
    
    # Test cases that commonly cause word cutting
    test_cases = [
        "Hello",  # Very short
        "Hi there",  # Short phrase
        "How are you doing today?",  # Medium sentence
        "This is a longer sentence that should test the TTS system's ability to handle more complex text without cutting words.",  # Long sentence
        "Testing punctuation! Does it work? Yes, it should.",  # Punctuation test
        "Numbers like 123 and symbols like @ should work too.",  # Special characters
    ]
    
    for voice in ["tara", "elise"]:
        logger.info(f"\n🎤 Testing voice: {voice}")
        
        # Create TTS instance with optimizations enabled
        tts = OptimizedOrpheusTTS(
            voice=voice,
            enable_resampling=True,
            add_silence_padding=True
        )
        
        for i, test_text in enumerate(test_cases, 1):
            logger.info(f"\n📝 Test {i}: '{test_text}'")
            
            try:
                # Create synthesis stream
                stream = tts.synthesize(test_text)
                
                # Mock audio emitter to capture output
                class MockAudioEmitter:
                    def __init__(self):
                        self.initialized = False
                        self.audio_data = b''
                        self.sample_rate = None
                        self.num_channels = None
                    
                    def initialize(self, request_id, sample_rate, num_channels, mime_type, **kwargs):
                        self.initialized = True
                        self.sample_rate = sample_rate
                        self.num_channels = num_channels
                        logger.info(f"🔧 Initialized: {sample_rate}Hz, {num_channels}ch, {mime_type}")
                    
                    def push(self, data):
                        self.audio_data += data
                        logger.info(f"📊 Received audio chunk: {len(data)} bytes")
                    
                    def flush(self):
                        logger.info(f"✅ Audio complete: {len(self.audio_data)} total bytes at {self.sample_rate}Hz")
                
                # Run synthesis
                emitter = MockAudioEmitter()
                await stream._run(emitter)
                
                # Validate results
                if emitter.initialized and emitter.audio_data:
                    duration_seconds = len(emitter.audio_data) / (emitter.sample_rate * 2)  # 16-bit samples
                    logger.info(f"🎵 Success: {duration_seconds:.2f}s of audio generated")
                else:
                    logger.error("❌ No audio generated")
                
            except Exception as e:
                logger.error(f"❌ Test failed: {e}")
            
            # Small delay between tests
            await asyncio.sleep(1)
    
    logger.info("\n✅ OptimizedOrpheusTTS test completed")

async def test_comparison():
    """Compare optimized vs basic settings."""
    logger.info("\n🔬 Running comparison test")
    
    test_text = "This is a test to compare audio quality."
    
    # Test with optimizations disabled
    logger.info("🔧 Testing with optimizations DISABLED")
    tts_basic = OptimizedOrpheusTTS(
        voice="tara",
        enable_resampling=False,
        add_silence_padding=False
    )
    
    # Test with optimizations enabled
    logger.info("🔧 Testing with optimizations ENABLED")
    tts_optimized = OptimizedOrpheusTTS(
        voice="tara",
        enable_resampling=True,
        add_silence_padding=True
    )
    
    for name, tts_instance in [("Basic", tts_basic), ("Optimized", tts_optimized)]:
        logger.info(f"\n🧪 Testing {name} configuration")
        
        try:
            stream = tts_instance.synthesize(test_text)
            
            class ComparisonEmitter:
                def __init__(self, name):
                    self.name = name
                    self.audio_size = 0
                    self.sample_rate = None
                
                def initialize(self, request_id, sample_rate, num_channels, mime_type, **kwargs):
                    self.sample_rate = sample_rate
                    logger.info(f"📊 {self.name}: {sample_rate}Hz, {num_channels}ch")
                
                def push(self, data):
                    self.audio_size += len(data)
                
                def flush(self):
                    duration = self.audio_size / (self.sample_rate * 2) if self.sample_rate else 0
                    logger.info(f"🎵 {self.name}: {self.audio_size} bytes, {duration:.2f}s")
            
            emitter = ComparisonEmitter(name)
            await stream._run(emitter)
            
        except Exception as e:
            logger.error(f"❌ {name} test failed: {e}")

if __name__ == "__main__":
    async def main():
        logger.info("🚀 Starting TTS optimization tests")
        
        try:
            await test_optimized_tts()
            await test_comparison()
            logger.info("\n🎉 All tests completed successfully!")
            
        except KeyboardInterrupt:
            logger.info("\n⏹️ Tests interrupted by user")
        except Exception as e:
            logger.error(f"\n💥 Test suite failed: {e}")
            sys.exit(1)
    
    # Run the tests
    asyncio.run(main())
