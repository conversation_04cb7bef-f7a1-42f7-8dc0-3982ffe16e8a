"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/WebCallingInterface.tsx":
/*!************************************************!*\
  !*** ./src/components/WebCallingInterface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebCallingInterface: () => (/* binding */ WebCallingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _NoAgentNotification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NoAgentNotification */ \"(app-pages-browser)/./src/components/NoAgentNotification.tsx\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/contexts-Cm1aSBTs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/hooks-OJtwh4jO.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/prefabs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/components-BeK2vIib.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _utils_audioOptimization__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/audioOptimization */ \"(app-pages-browser)/./src/utils/audioOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ WebCallingInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n// API URL configuration\nconst API_URL = \"http://192.168.1.107:9090\" || 0;\n\n\n\n\n\n\n\nfunction WebCallingInterface(param) {\n    let { agentId, agentName, userId, onEndCall } = param;\n    _s();\n    const [room] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room());\n    const [isSpawningWorker, setIsSpawningWorker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [workerError, setWorkerError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const onConnectButtonClicked = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"WebCallingInterface.useCallback[onConnectButtonClicked]\": async ()=>{\n            try {\n                setIsSpawningWorker(true);\n                setWorkerError(\"\");\n                // CRITICAL FIX: Use optimized WebRTC connection to prevent audio mode switching artifacts\n                await (0,_utils_audioOptimization__WEBPACK_IMPORTED_MODULE_4__.optimizeWebRTCConnection)({\n                    \"WebCallingInterface.useCallback[onConnectButtonClicked]\": async ()=>{\n                        // Step 1: Request microphone permissions with audio processing disabled\n                        console.log(\"🎤 Requesting microphone permissions...\");\n                        try {\n                            const stream = await navigator.mediaDevices.getUserMedia({\n                                audio: {\n                                    echoCancellation: false,\n                                    noiseSuppression: false,\n                                    autoGainControl: false,\n                                    sampleRate: 48000,\n                                    channelCount: 1 // FIXED: Mono audio\n                                },\n                                video: false\n                            });\n                            // Stop the stream immediately - we just needed to check permissions\n                            stream.getTracks().forEach({\n                                \"WebCallingInterface.useCallback[onConnectButtonClicked]\": (track)=>track.stop()\n                            }[\"WebCallingInterface.useCallback[onConnectButtonClicked]\"]);\n                            console.log(\"✅ Microphone permissions granted with optimized audio settings\");\n                        } catch (permissionError) {\n                            console.error(\"❌ Microphone permission denied:\", permissionError);\n                            throw new Error(\"Microphone permission is required for voice calling. Please allow microphone access and try again.\");\n                        }\n                        // Step 2: Get connection details first to generate unique room name\n                        const url = new URL(\"\".concat(API_URL, \"/api/connection-details\"));\n                        // Add agent ID and user ID to the connection details request for room isolation\n                        if (agentId) {\n                            url.searchParams.set('agentId', agentId);\n                        }\n                        if (userId) {\n                            url.searchParams.set('userId', userId);\n                        }\n                        const response = await fetch(url.toString());\n                        console.log('🔗 Connection details response status:', response.status);\n                        if (!response.ok) {\n                            const errorData = await response.text();\n                            console.error('🔗 Connection details error:', errorData);\n                            throw new Error(\"Failed to get connection details: \".concat(response.status, \" - \").concat(errorData));\n                        }\n                        const connectionDetailsData = await response.json();\n                        console.log('🔗 Connection details received:', connectionDetailsData);\n                        // Step 3: Spawn LiveKit worker for the specific room (FIXED: now includes room_name)\n                        if (agentId) {\n                            console.log(\"🤖 Spawning LiveKit worker for agent ID:\", agentId, \"in room:\", connectionDetailsData.roomName);\n                            const workerResponse = await fetch(\"\".concat(API_URL, \"/api/livekit/spawn\"), {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    agent_id: agentId,\n                                    room_name: connectionDetailsData.roomName\n                                })\n                            });\n                            if (!workerResponse.ok) {\n                                const errorData = await workerResponse.json();\n                                throw new Error(errorData.error || 'LiveKit worker not available for this agent');\n                            }\n                            const workerResult = await workerResponse.json();\n                            console.log('✅ LiveKit worker spawned successfully:', workerResult);\n                        }\n                        // Step 4: Connect to LiveKit room and enable microphone with optimized audio settings\n                        await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);\n                        await room.localParticipant.setMicrophoneEnabled(true, {\n                            echoCancellation: false,\n                            noiseSuppression: false,\n                            autoGainControl: false,\n                            sampleRate: 48000,\n                            channelCount: 1 // FIXED: Mono audio\n                        });\n                        console.log(\"🎉 Successfully connected to voice assistant with optimized audio\");\n                    }\n                }[\"WebCallingInterface.useCallback[onConnectButtonClicked]\"]);\n            } catch (error) {\n                console.error('🚨 Error connecting to voice assistant:', error);\n                setWorkerError(error instanceof Error ? error.message : 'Failed to connect');\n            } finally{\n                setIsSpawningWorker(false);\n            }\n        }\n    }[\"WebCallingInterface.useCallback[onConnectButtonClicked]\"], [\n        room,\n        agentId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"WebCallingInterface.useEffect\": ()=>{\n            room.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.MediaDevicesError, onDeviceFailure);\n            return ({\n                \"WebCallingInterface.useEffect\": ()=>{\n                    room.off(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.MediaDevicesError, onDeviceFailure);\n                }\n            })[\"WebCallingInterface.useEffect\"];\n        }\n    }[\"WebCallingInterface.useEffect\"], [\n        room\n    ]);\n    function onDeviceFailure(error) {\n        console.error(\"Device failure:\", error);\n        // More specific error message based on the error type\n        let errorMessage = \"Microphone access error. \";\n        if (error.name === \"NotAllowedError\") {\n            errorMessage += \"Please allow microphone access in your browser settings and refresh the page.\";\n        } else if (error.name === \"NotFoundError\") {\n            errorMessage += \"No microphone found. Please connect a microphone and try again.\";\n        } else if (error.name === \"NotReadableError\") {\n            errorMessage += \"Microphone is being used by another application. Please close other apps using your microphone.\";\n        } else {\n            errorMessage += \"Please check your microphone settings and refresh the page.\";\n        }\n        setWorkerError(errorMessage);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-white flex flex-col overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.R.Provider, {\n            value: room,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleVoiceAssistant, {\n                onConnectButtonClicked: onConnectButtonClicked,\n                isSpawningWorker: isSpawningWorker,\n                workerError: workerError,\n                agentName: agentName,\n                onEndCall: onEndCall\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(WebCallingInterface, \"Llct4kKfh/UAd25gIoT89xkY21k=\");\n_c = WebCallingInterface;\nfunction SimpleVoiceAssistant(props) {\n    _s1();\n    const { state: agentState } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_6__.V)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full min-h-0 flex flex-col\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n            mode: \"wait\",\n            children: agentState === \"disconnected\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"flex flex-col items-center justify-center flex-1 p-3 sm:p-4 lg:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4 sm:mb-6 lg:mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm sm:text-lg lg:text-xl font-semibold text-gray-900 mb-1 sm:mb-2\",\n                                children: props.agentName\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-gray-600 mb-2\",\n                                children: \"Ready to start conversation\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-2 sm:p-3 mx-auto max-w-xs sm:max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-1 sm:gap-2 text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm\",\n                                            children: \"Microphone access required\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 13\n                    }, this),\n                    props.workerError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-2 sm:p-3 mb-3 sm:mb-4 w-full max-w-xs sm:max-w-md text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-xs sm:text-sm\",\n                            children: props.workerError\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"flex items-center justify-center gap-2 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm lg:text-base w-full max-w-xs sm:max-w-sm\",\n                        onClick: ()=>props.onConnectButtonClicked(),\n                        disabled: props.isSpawningWorker,\n                        children: props.isSpawningWorker ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden xs:inline sm:hidden lg:inline\",\n                                    children: \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"xs:hidden sm:inline lg:hidden\",\n                                    children: \"Connecting\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden xs:inline sm:hidden lg:inline\",\n                                    children: \"Start Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"xs:hidden sm:inline lg:hidden\",\n                                    children: \"Call\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"disconnected\", true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 195,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"flex flex-col w-full h-full min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-2 sm:p-3 lg:p-4 bg-white border-b border-gray-200 flex-shrink-0 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 text-xs sm:text-sm lg:text-base truncate\",\n                                                children: props.agentName\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-600 hidden sm:inline\",\n                                                        children: \"Connected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-2 sm:mx-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveAgentStateIndicator, {\n                                    state: agentState\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 sm:gap-2 flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block [&_.lk-button]:bg-gray-50 [&_.lk-button]:hover:bg-gray-100 [&_.lk-button]:border-gray-200 [&_.lk-button]:text-gray-700 [&_.lk-button]:shadow-sm [&_.lk-button]:w-7 [&_.lk-button]:h-7 [&_.lk-button]:rounded-md [&_.lk-button]:transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.VoiceAssistantControlBar, {\n                                            controls: {\n                                                leave: false\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_13__.D, {\n                                        className: \"bg-red-500 hover:bg-red-600 text-white rounded-md w-7 h-7 sm:w-8 sm:h-8 shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center\",\n                                        onClick: props.onEndCall,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 bg-white flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 sm:w-10 sm:h-10 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm sm:text-base font-medium text-gray-900 mb-2\",\n                                    children: [\n                                        \"Connected to \",\n                                        props.agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs sm:text-sm text-gray-600\",\n                                    children: \"Voice conversation is active\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_13__.R, {}, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoAgentNotification__WEBPACK_IMPORTED_MODULE_1__.NoAgentNotification, {\n                        state: agentState\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"connected\", true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 250,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s1(SimpleVoiceAssistant, \"XOml166M2vIO8FJctr0RSKfKDkM=\", false, function() {\n    return [\n        _livekit_components_react__WEBPACK_IMPORTED_MODULE_6__.V\n    ];\n});\n_c1 = SimpleVoiceAssistant;\n// Responsive Agent State Indicator\nfunction ResponsiveAgentStateIndicator(param) {\n    let { state } = param;\n    const getStateInfo = ()=>{\n        switch(state){\n            case \"listening\":\n                return {\n                    text: \"Listening\",\n                    shortText: \"Mic\",\n                    color: \"bg-blue-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            case \"thinking\":\n                return {\n                    text: \"Thinking\",\n                    shortText: \"AI\",\n                    color: \"bg-yellow-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            case \"speaking\":\n                return {\n                    text: \"Speaking\",\n                    shortText: \"Talk\",\n                    color: \"bg-green-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                };\n            default:\n                return {\n                    text: \"Ready\",\n                    shortText: \"Ready\",\n                    color: \"bg-gray-400\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n        }\n    };\n    const stateInfo = getStateInfo();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-1 sm:gap-2 px-1.5 sm:px-2 py-1 bg-gray-50 border border-gray-200 rounded-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full \".concat(stateInfo.color, \" \").concat(state === 'thinking' ? 'animate-pulse' : '')\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-600 hidden sm:inline\",\n                children: stateInfo.text\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-600 sm:hidden\",\n                children: stateInfo.shortText\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ResponsiveAgentStateIndicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WebCallingInterface\");\n$RefreshReg$(_c1, \"SimpleVoiceAssistant\");\n$RefreshReg$(_c2, \"ResponsiveAgentStateIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebCallingInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/audioOptimization.ts":
/*!****************************************!*\
  !*** ./src/utils/audioOptimization.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupAudioContext: () => (/* binding */ cleanupAudioContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeAudioContext: () => (/* binding */ initializeAudioContext),\n/* harmony export */   initializeAudioElementSmoothly: () => (/* binding */ initializeAudioElementSmoothly),\n/* harmony export */   optimizeMicrophoneActivation: () => (/* binding */ optimizeMicrophoneActivation),\n/* harmony export */   optimizeWebRTCConnection: () => (/* binding */ optimizeWebRTCConnection),\n/* harmony export */   preInitializeAudioForWebRTC: () => (/* binding */ preInitializeAudioForWebRTC),\n/* harmony export */   preloadAudioSmoothly: () => (/* binding */ preloadAudioSmoothly)\n/* harmony export */ });\n/**\r\n * Audio optimization utilities to prevent click sounds and audio artifacts\r\n * during initialization and playback in web applications.\r\n *\r\n * CRITICAL FIX: Handles browser audio context mode switching that causes artifacts\r\n * when transitioning from media playback to WebRTC communications mode.\r\n */ // Global audio context management\nlet globalAudioContext = null;\nlet isWebRTCActive = false;\n/**\r\n * CRITICAL FIX: Pre-initialize audio context to prevent mode switching artifacts\r\n * This must be called BEFORE any WebRTC connection to establish the correct audio mode\r\n */ const preInitializeAudioForWebRTC = async ()=>{\n    try {\n        console.log('🔧 Pre-initializing audio context for WebRTC to prevent mode switching artifacts');\n        // Create a temporary audio context to \"claim\" the audio system\n        // This prevents the browser from switching modes when WebRTC starts\n        const tempContext = new (window.AudioContext || window.webkitAudioContext)({\n            latencyHint: 'interactive',\n            sampleRate: 48000 // CRITICAL: Use 48kHz to match WebRTC standard and prevent resampling\n        });\n        // Ensure context is active\n        if (tempContext.state === 'suspended') {\n            await tempContext.resume();\n        }\n        // Create a very brief silent tone to activate the audio pipeline\n        // This \"warms up\" the audio system in the correct mode\n        const buffer = tempContext.createBuffer(1, 480, 48000); // 10ms at 48kHz\n        const source = tempContext.createBufferSource();\n        const gainNode = tempContext.createGain();\n        // Set very low volume to avoid any audible sound\n        gainNode.gain.setValueAtTime(0.001, tempContext.currentTime);\n        source.buffer = buffer;\n        source.connect(gainNode);\n        gainNode.connect(tempContext.destination);\n        source.start();\n        // Wait for the audio system to stabilize\n        await new Promise((resolve)=>setTimeout(resolve, 50));\n        // Keep this context as our global context\n        globalAudioContext = tempContext;\n        isWebRTCActive = true;\n        console.log('✅ Audio context pre-initialized for WebRTC at 48kHz');\n    } catch (error) {\n        console.error('❌ Failed to pre-initialize audio context:', error);\n        throw error;\n    }\n};\n/**\r\n * Initialize the global audio context smoothly to prevent click sounds\r\n */ const initializeAudioContext = async ()=>{\n    if (globalAudioContext && globalAudioContext.state !== 'closed') {\n        return globalAudioContext;\n    }\n    try {\n        // CRITICAL FIX: Use 48kHz for WebRTC compatibility and to prevent browser mode switching\n        globalAudioContext = new (window.AudioContext || window.webkitAudioContext)({\n            latencyHint: 'interactive',\n            sampleRate: 48000 // FIXED: Use 48kHz to match WebRTC and prevent resampling artifacts\n        });\n        // Ensure context is running\n        if (globalAudioContext.state === 'suspended') {\n            await globalAudioContext.resume();\n        }\n        // Create a brief silent buffer to \"warm up\" the audio system\n        const buffer = globalAudioContext.createBuffer(1, 1, globalAudioContext.sampleRate);\n        const source = globalAudioContext.createBufferSource();\n        source.buffer = buffer;\n        source.connect(globalAudioContext.destination);\n        source.start();\n        console.log('🎵 Audio context initialized smoothly at 48kHz');\n        return globalAudioContext;\n    } catch (error) {\n        console.error('Failed to initialize audio context:', error);\n        throw error;\n    }\n};\n// Volume ramping function removed per user request - no longer manipulating audio volume\n/**\r\n * Smooth audio element initialization to prevent click sounds\r\n */ const initializeAudioElementSmoothly = async (audioElement)=>{\n    // Ensure audio context is initialized\n    await initializeAudioContext();\n    // Set initial properties\n    audioElement.volume = 0;\n    audioElement.preload = 'none';\n    // Add event listeners for basic playback\n    const handlePlay = ()=>{\n        console.log('Audio started playing');\n    };\n    const handlePause = ()=>{\n        // Smooth fade out when pausing\n        const currentVolume = audioElement.volume;\n        const fadeOut = (volume)=>{\n            if (volume > 0 && !audioElement.paused) {\n                audioElement.volume = Math.max(0, volume - 0.1);\n                setTimeout(()=>fadeOut(audioElement.volume), 10);\n            }\n        };\n        fadeOut(currentVolume);\n    };\n    audioElement.addEventListener('play', handlePlay);\n    audioElement.addEventListener('pause', handlePause);\n    // Return cleanup function\n    return ()=>{\n        audioElement.removeEventListener('play', handlePlay);\n        audioElement.removeEventListener('pause', handlePause);\n    };\n};\n/**\r\n * CRITICAL FIX: Optimize WebRTC connection to prevent audio mode switching artifacts\r\n */ const optimizeWebRTCConnection = async (connectionFunction)=>{\n    try {\n        console.log('🔧 Starting optimized WebRTC connection to prevent audio artifacts');\n        // Step 1: Pre-initialize audio context in WebRTC mode\n        await preInitializeAudioForWebRTC();\n        // Step 2: Brief delay to allow audio system to stabilize in the correct mode\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // Step 3: Execute the WebRTC connection\n        await connectionFunction();\n        // Step 4: Additional stabilization delay\n        await new Promise((resolve)=>setTimeout(resolve, 150));\n        console.log('✅ WebRTC connection optimized - audio artifacts should be prevented');\n    } catch (error) {\n        console.error('❌ Error during optimized WebRTC connection:', error);\n        throw error;\n    }\n};\n/**\r\n * Optimize microphone activation to prevent click sounds\r\n */ const optimizeMicrophoneActivation = async (micActivationFunction)=>{\n    try {\n        // Initialize audio context first (will use existing WebRTC context if available)\n        await initializeAudioContext();\n        // Brief delay to allow audio system to stabilize\n        await new Promise((resolve)=>setTimeout(resolve, 50));\n        // Activate microphone\n        await micActivationFunction();\n        // Additional delay for microphone to fully initialize\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        console.log('🎤 Microphone activated with click prevention');\n    } catch (error) {\n        console.error('Error during optimized microphone activation:', error);\n        throw error;\n    }\n};\n/**\r\n * CRITICAL FIX: Clean up audio context when disconnecting to prevent lingering artifacts\r\n */ const cleanupAudioContext = async ()=>{\n    try {\n        if (globalAudioContext && globalAudioContext.state !== 'closed') {\n            console.log('🧹 Cleaning up audio context to prevent lingering artifacts');\n            // Gradually fade out any remaining audio\n            const gainNode = globalAudioContext.createGain();\n            gainNode.gain.setValueAtTime(1, globalAudioContext.currentTime);\n            gainNode.gain.exponentialRampToValueAtTime(0.001, globalAudioContext.currentTime + 0.1);\n            // Wait for fade out\n            await new Promise((resolve)=>setTimeout(resolve, 150));\n            // Close the context\n            await globalAudioContext.close();\n            globalAudioContext = null;\n            isWebRTCActive = false;\n            console.log('✅ Audio context cleaned up successfully');\n        }\n    } catch (error) {\n        console.error('❌ Error cleaning up audio context:', error);\n    }\n};\n/**\r\n * Preload and optimize audio for smooth playback\r\n */ const preloadAudioSmoothly = async (audioUrl)=>{\n    const audio = new Audio();\n    // Initialize smoothly\n    await initializeAudioElementSmoothly(audio);\n    return new Promise((resolve, reject)=>{\n        const handleCanPlay = ()=>{\n            audio.removeEventListener('canplaythrough', handleCanPlay);\n            audio.removeEventListener('error', handleError);\n            resolve(audio);\n        };\n        const handleError = (e)=>{\n            audio.removeEventListener('canplaythrough', handleCanPlay);\n            audio.removeEventListener('error', handleError);\n            reject(e);\n        };\n        audio.addEventListener('canplaythrough', handleCanPlay);\n        audio.addEventListener('error', handleError);\n        audio.src = audioUrl;\n        audio.load();\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    initializeAudioContext,\n    initializeAudioElementSmoothly,\n    optimizeMicrophoneActivation,\n    preloadAudioSmoothly\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/audioOptimization.ts\n"));

/***/ })

});