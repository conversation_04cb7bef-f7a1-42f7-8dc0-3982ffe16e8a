import requests
import base64
import io
import json
import logging
from flask import jsonify, send_file

# Add audio processing imports
try:
    from pydub import AudioSegment
    from pydub.utils import make_chunks
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("Warning: pydub not available. Audio format conversion will be limited.")
    # Create dummy classes to prevent import errors
    class AudioSegment:
        @staticmethod
        def from_file(*args, **kwargs):
            raise ImportError("pydub not available")
        def set_frame_rate(self, *args):
            return self
        def set_channels(self, *args):
            return self
        def set_sample_width(self, *args):
            return self
        def export(self, *args, **kwargs):
            pass

# Set up logging
logger = logging.getLogger(__name__)

def create_wav_header(data_length, sample_rate=24000, channels=1, bits_per_sample=16):
    """Create a simple WAV header for raw audio data - FIXED: Use 24000 Hz for LiveKit compatibility"""
    import struct

    # Calculate some values
    byte_rate = sample_rate * channels * bits_per_sample // 8
    block_align = channels * bits_per_sample // 8

    # FIXED: Simple WAV header without padding to prevent artifacts
    total_data_length = data_length

    # Standard WAV header structure
    header = b'RIFF'  # Chunk ID
    header += struct.pack('<I', 36 + total_data_length)  # Chunk size
    header += b'WAVE'  # Format
    header += b'fmt '  # Subchunk1 ID
    header += struct.pack('<I', 16)  # Subchunk1 size (16 for PCM)
    header += struct.pack('<H', 1)   # Audio format (1 for PCM)
    header += struct.pack('<H', channels)  # Number of channels
    header += struct.pack('<I', sample_rate)  # Sample rate
    header += struct.pack('<I', byte_rate)  # Byte rate
    header += struct.pack('<H', block_align)  # Block align
    header += struct.pack('<H', bits_per_sample)  # Bits per sample
    header += b'data'  # Subchunk2 ID
    header += struct.pack('<I', total_data_length)  # Subchunk2 size

    return header

def convert_raw_audio_to_wav(raw_audio_bytes):
    """Convert raw audio data to properly formatted WAV - SIMPLIFIED to prevent artifacts"""
    try:
        # FIXED: Assume the raw audio is 16-bit, 24000 Hz, mono (Orpheus TTS standard)
        wav_header = create_wav_header(len(raw_audio_bytes))

        # FIXED: Simple combination without padding to prevent artifacts
        wav_bytes = wav_header + raw_audio_bytes

        logger.info(f"✅ Created simple WAV file: header={len(wav_header)} bytes, data={len(raw_audio_bytes)} bytes, total={len(wav_bytes)} bytes")

        return wav_bytes
    except Exception as e:
        logger.error(f"Error creating WAV from raw audio: {e}")
        return raw_audio_bytes

def convert_to_browser_compatible_audio(audio_bytes):
    """SIMPLIFIED: Minimal audio processing to prevent artifacts"""
    # First check if it's already a valid WAV file
    if len(audio_bytes) >= 12 and audio_bytes[:4] == b'RIFF' and audio_bytes[8:12] == b'WAVE':
        logger.info("Audio is already a valid WAV file - returning as-is to prevent artifacts")
        return audio_bytes, 'audio/wav'

    # If not a WAV file, try to add basic WAV header
    logger.info("Audio is not a valid WAV file, adding basic WAV header")

    if not PYDUB_AVAILABLE:
        logger.warning("pydub not available - using basic WAV header creation")
        return convert_raw_audio_to_wav(audio_bytes), 'audio/wav'

    try:
        # CRITICAL FIX: Minimal processing to prevent artifacts
        # Only ensure proper format without any audio manipulation

        # Try to load as raw PCM first (assume 24kHz, 16-bit, mono from Orpheus)
        try:
            # Create AudioSegment from raw PCM data
            audio = AudioSegment(
                data=audio_bytes,
                sample_width=2,  # 16-bit
                frame_rate=24000,  # 24kHz
                channels=1  # Mono
            )
            logger.info("Successfully loaded as raw PCM data")
        except:
            # Fallback: try to load as any audio format
            audio = AudioSegment.from_file(io.BytesIO(audio_bytes))
            logger.info("Loaded using AudioSegment auto-detection")

        # FIXED: Only ensure correct format - NO audio processing
        # Just standardize format without any effects or normalization
        if audio.frame_rate != 24000 or audio.channels != 1 or audio.sample_width != 2:
            logger.info(f"Converting format: {audio.frame_rate}Hz -> 24000Hz, {audio.channels}ch -> 1ch, {audio.sample_width*8}bit -> 16bit")
            standardized_audio = audio.set_frame_rate(24000).set_channels(1).set_sample_width(2)
        else:
            logger.info("Audio already in correct format - no conversion needed")
            standardized_audio = audio

        # Export as WAV with minimal parameters
        output_buffer = io.BytesIO()
        standardized_audio.export(
            output_buffer,
            format="wav"
            # REMOVED all custom parameters to prevent artifacts
        )
        output_buffer.seek(0)

        converted_bytes = output_buffer.getvalue()
        logger.info(f"✅ Minimal audio processing complete: {len(audio_bytes)} -> {len(converted_bytes)} bytes")
        logger.info(f"Final format: {standardized_audio.frame_rate}Hz, {standardized_audio.channels}ch, {standardized_audio.sample_width*8}bit")

        return converted_bytes, 'audio/wav'

    except Exception as e:
        logger.error(f"Error in minimal audio processing: {e}")
        # Fallback to basic WAV header creation
        logger.info("Falling back to basic WAV header creation")
        return convert_raw_audio_to_wav(audio_bytes), 'audio/wav'

def get_tts_api_config(voice):
    """Get TTS API configuration based on voice selection"""
    voice_lower = voice.lower()
    
    if voice_lower in ['tara', 'tara_async']:
        # Determine if streaming or non-streaming
        is_streaming = voice_lower == 'tara'
        endpoint = 'predict' if is_streaming else 'async_predict'  # FIXED: Use 'predict' not 'stream'
        
        return {
            'url': f'https://model-4w7jnzyw.api.baseten.co/environments/production/{endpoint}',  # FIXED: Correct Tara URL
            'api_key': 'k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw',
            'voice_id': 'tara',
            'is_streaming': is_streaming
        }
    elif voice_lower in ['elise', 'elise_async']:
        # Determine if streaming or non-streaming
        is_streaming = voice_lower == 'elise'
        endpoint = 'predict' if is_streaming else 'async_predict'  # FIXED: Use 'predict' not 'stream'
        
        return {
            'url': f'https://model-5qenjjpq.api.baseten.co/environments/production/{endpoint}',  # FIXED: Correct Elise URL
            'api_key': 'k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw',
            'voice_id': 'elise',
            'is_streaming': is_streaming
        }
    else:
        # Default to Tara non-streaming for safety
        return {
            'url': 'https://model-4w7jnzyw.api.baseten.co/environments/production/async_predict',  # FIXED: Correct Tara URL
            'api_key': 'k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw',
            'voice_id': 'tara',
            'is_streaming': False
        }

def test_tts(text, voice='tara'):
    """Test TTS with provided text and voice - WITH WORD-PROTECTION PREPROCESSING"""
    try:
        if not text:
            return jsonify({"error": "Text is required"}), 400
        
        # WORD-PROTECTION: Apply text preprocessing to prevent word cutting
        original_text = text
        protected_text = optimize_text_for_tts_model(text, model_type='baseten')
        
        # Get API configuration based on voice selection
        api_config = get_tts_api_config(voice)
        
        logger.info(f"Testing TTS with voice {voice}")
        logger.info(f"Original text: {original_text[:50]}...")
        logger.info(f"Protected text: {protected_text[:50]}...")
        
        # FIXED: Format the request payload based on streaming vs non-streaming mode
        if api_config['is_streaming']:
            # Streaming mode: use direct payload format
            request_payload = {
                'voice': api_config['voice_id'],
                'prompt': protected_text,  # Use protected text
                'max_tokens': 10000,
                'temperature': 0.7,  # Add temperature for more consistent generation
                'top_p': 0.9  # Add top_p for better word completion
            }
            logger.info(f"Using streaming payload format for {voice}")
        else:
            # Non-streaming mode: wrap payload in model_input
            request_payload = {
                'model_input': {
                    'voice': api_config['voice_id'],
                    'prompt': protected_text,  # Use protected text
                    'max_tokens': 10000,
                    'temperature': 0.7,  # Add temperature for more consistent generation
                    'top_p': 0.9  # Add top_p for better word completion
                }
            }
            logger.info(f"Using non-streaming payload format (wrapped in model_input) for {voice}")
        
        logger.info(f"Sending word-protected request to {voice} TTS API")
        
        # Make request to appropriate TTS API based on voice - NO TIMEOUT
        response = requests.post(
            api_config['url'],
            headers={"Authorization": f"Api-Key {api_config['api_key']}"},
            json=request_payload
            # REMOVED 30-second timeout - let TTS generation complete
        )
        
        # Log response details
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content type: {response.headers.get('content-type', 'unknown')}")
        logger.info(f"Response content length: {len(response.content)}")
        
        # Check the first few bytes to see if it's actually a WAV file
        if len(response.content) >= 12:
            header_bytes = response.content[:12]
            header_str = ''.join([chr(b) if 32 <= b <= 126 else f'\\x{b:02x}' for b in header_bytes])
            logger.info(f"Response header bytes: {header_str}")
            logger.info(f"First 12 bytes: {list(header_bytes)}")
            
            # Check if it's a valid WAV file
            is_wav = response.content[:4] == b'RIFF' and response.content[8:12] == b'WAVE'
            logger.info(f"Is valid WAV file: {is_wav}")
        else:
            logger.info(f"Response too short: {len(response.content)} bytes")
        
        if response.status_code != 200:
            logger.error(f"TTS API error: {response.text}")
            return jsonify({"error": f"TTS API error: {response.text}"}), 500
        
        # Check content type and handle accordingly
        content_type = response.headers.get('Content-Type', '')
        
        if 'audio/wav' in content_type:
            # Response claims to be audio - but let's verify
            logger.info(f"Response claims to be audio. Content-Type: {content_type}")
            audio_content = response.content
            if audio_content:
                # Check if it's actually a WAV file
                if len(audio_content) >= 12 and audio_content[:4] == b'RIFF' and audio_content[8:12] == b'WAVE':
                    logger.info("Valid WAV file detected, converting to browser-compatible format with anti-cut protection")
                    converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                    return send_file(
                        io.BytesIO(converted_audio),
                        mimetype=content_type,
                        as_attachment=False
                    )
                else:
                    logger.warning("Content-Type says audio/wav but data is not a valid WAV file!")
                    logger.info("Treating as raw audio data and attempting to convert to proper WAV format with anti-cut protection")
                    
                    # Try to convert raw audio data to proper WAV format
                    try:
                        converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                        return send_file(
                            io.BytesIO(converted_audio),
                            mimetype=content_type,
                            as_attachment=False
                        )
                    except Exception as e:
                        logger.error(f"Error converting raw audio to WAV: {e}")
                        
                        # Fallback: try to parse as JSON
                        try:
                            response_data = response.json()
                            logger.info(f"Successfully parsed as JSON. Keys: {list(response_data.keys())}")
                            
                            # Look for audio data in JSON
                            audio_data = None
                            if 'audio' in response_data:
                                audio_data = response_data['audio']
                            elif 'model_output' in response_data:
                                if isinstance(response_data['model_output'], dict):
                                    audio_data = response_data['model_output'].get('audio')
                                else:
                                    # model_output might be the audio data directly
                                    audio_data = response_data['model_output']
                            
                            if audio_data:
                                logger.info(f"Found audio data, length: {len(audio_data) if isinstance(audio_data, str) else 'unknown'}")
                                try:
                                    audio_bytes = base64.b64decode(audio_data)
                                    logger.info(f"Decoded audio bytes: {len(audio_bytes)}")
                                    # Apply enhanced audio processing with anti-cut protection
                                    converted_audio, content_type = convert_to_browser_compatible_audio(audio_bytes)
                                    return send_file(
                                        io.BytesIO(converted_audio),
                                        mimetype=content_type,
                                        as_attachment=False
                                    )
                                except Exception as e:
                                    logger.error(f"Error decoding base64 audio: {e}")
                                    return jsonify({"error": "Error decoding audio data"}), 500
                            else:
                                logger.error("No audio data found in JSON response")
                                return jsonify({"error": "No audio data in response"}), 500
                        except json.JSONDecodeError as e:
                            logger.error(f"Cannot parse as JSON either: {e}")
                            return jsonify({"error": "Invalid response format"}), 500
            else:
                return jsonify({"error": "No audio content received"}), 500
        
        elif 'application/json' in content_type:
            # Try to parse JSON response
            try:
                response_data = response.json()
                logger.info(f"Response keys: {list(response_data.keys())}")
                
                # Try different possible locations for the audio data
                audio_data = None
                
                # Check for audio in the top level
                if 'audio' in response_data:
                    audio_data = response_data.get('audio')
                # Check for audio in model_output
                elif 'model_output' in response_data and isinstance(response_data['model_output'], dict):
                    audio_data = response_data['model_output'].get('audio')
                # Check for audio in output
                elif 'output' in response_data and isinstance(response_data['output'], dict):
                    audio_data = response_data['output'].get('audio')
                # Check for base64_audio
                elif 'base64_audio' in response_data:
                    audio_data = response_data.get('base64_audio')
                    
                if audio_data:
                    # Decode base64 audio and return raw audio
                    try:
                        audio_bytes = base64.b64decode(audio_data)
                        # Apply enhanced audio processing with anti-cut protection
                        converted_audio, content_type = convert_to_browser_compatible_audio(audio_bytes)
                        return send_file(
                            io.BytesIO(converted_audio),
                            mimetype=content_type,
                            as_attachment=False
                        )
                    except Exception as e:
                        logger.error(f"Error processing audio: {e}")
                        return jsonify({"error": "Error processing audio data"}), 500
                else:
                    logger.error("No audio data found in JSON response")
                    return jsonify({"error": "No audio data in response"}), 500
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Response not valid JSON: {e}")
                # Fallback to treating as raw audio with enhanced processing
                audio_content = response.content
                if audio_content:
                    converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                    return send_file(
                        io.BytesIO(converted_audio),
                        mimetype=content_type,
                        as_attachment=False
                    )
                else:
                    return jsonify({"error": "No audio content received"}), 500
        else:
            # Unknown content type - try treating as raw audio with enhanced processing
            logger.info(f"Unknown content type: {content_type}, treating as raw audio with enhanced processing")
            audio_content = response.content
            if audio_content:
                converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                return send_file(
                    io.BytesIO(converted_audio),
                    mimetype=content_type,
                    as_attachment=False
                )
            else:
                return jsonify({"error": "No audio content received"}), 500
                
    except Exception as e:
        logger.error(f"Error processing TTS request: {str(e)}")
        return jsonify({"error": f"Failed to process TTS request: {str(e)}"}), 500

def generate_tts(text, voice='tara'):
    """Generate TTS audio for agents and return audio data - WITH WORD-PROTECTION PREPROCESSING"""
    try:
        if not text:
            return jsonify({"error": "Text is required"}), 400
        
        # WORD-PROTECTION: Apply text preprocessing to prevent word cutting
        original_text = text
        protected_text = optimize_text_for_tts_model(text, model_type='baseten')
        
        # Get API configuration based on voice selection
        api_config = get_tts_api_config(voice)
        
        logger.info(f"Generating TTS with voice {voice}")
        logger.info(f"Original text: {original_text[:100]}...")
        logger.info(f"Protected text: {protected_text[:100]}...")
        
        # FIXED: Format the request payload based on streaming vs non-streaming mode
        if api_config['is_streaming']:
            # Streaming mode: use direct payload format
            request_payload = {
                'voice': api_config['voice_id'],
                'prompt': protected_text,  # Use protected text
                'max_tokens': 10000,
                'temperature': 0.7,  # Add temperature for more consistent generation
                'top_p': 0.9  # Add top_p for better word completion
            }
        else:
            # Non-streaming mode: wrap payload in model_input
            request_payload = {
                'model_input': {
                    'voice': api_config['voice_id'],
                    'prompt': protected_text,  # Use protected text
                    'max_tokens': 10000,
                    'temperature': 0.7,  # Add temperature for more consistent generation
                    'top_p': 0.9  # Add top_p for better word completion
                }
            }
        
        # Make request to TTS API - NO TIMEOUT
        response = requests.post(
            api_config['url'],
            headers={"Authorization": f"Api-Key {api_config['api_key']}"},
            json=request_payload
            # REMOVED 30-second timeout - let TTS generation complete
        )
        
        if response.status_code == 200:
            # Check content type
            content_type = response.headers.get('Content-Type', '')
            
            if 'audio/wav' in content_type:
                # Raw audio response - apply enhanced processing
                audio_content = response.content
                if audio_content:
                    converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                    return send_file(
                        io.BytesIO(converted_audio),
                        mimetype=content_type,
                        as_attachment=False
                    )
                else:
                    return jsonify({"error": "No audio content received"}), 500
            else:
                # Try JSON response
                try:
                    response_data = response.json()
                    audio_data = None
                    
                    if 'audio' in response_data:
                        audio_data = response_data['audio']
                    elif 'base64_audio' in response_data:
                        audio_data = response_data['base64_audio']
                    elif 'model_output' in response_data and isinstance(response_data['model_output'], dict):
                        audio_data = response_data['model_output'].get('audio')
                    
                    if audio_data:
                        # Decode base64 audio and apply enhanced processing
                        try:
                            audio_bytes = base64.b64decode(audio_data)
                            converted_audio, content_type = convert_to_browser_compatible_audio(audio_bytes)
                            return send_file(
                                io.BytesIO(converted_audio),
                                mimetype=content_type,
                                as_attachment=False
                            )
                        except Exception as e:
                            logger.error(f"Error processing audio: {e}")
                            return jsonify({"error": "Error processing audio data"}), 500
                    else:
                        return jsonify({"error": "No audio data in response"}), 500
                        
                except json.JSONDecodeError:
                    # Response might be raw audio - apply enhanced processing
                    audio_content = response.content
                    if audio_content:
                        converted_audio, content_type = convert_to_browser_compatible_audio(audio_content)
                        return send_file(
                            io.BytesIO(converted_audio),
                            mimetype=content_type,
                            as_attachment=False
                        )
                    else:
                        return jsonify({"error": "No audio content received"}), 500
        else:
            return jsonify({"error": f"TTS API error: {response.text}"}), 500
                
    except Exception as e:
        logger.error(f"Error generating TTS: {str(e)}")
        return jsonify({"error": f"TTS generation failed: {str(e)}"}), 500

def get_available_voices():
    """Get list of available voices"""
    try:
        voices = [
            {
                'id': 'tara',  # FIXED: Lowercase to match TTS system
                'name': 'Tara (Streaming)',
                'description': 'Female voice, warm and professional - Streaming version'
            },
            {
                'id': 'elise',  # FIXED: Lowercase to match TTS system
                'name': 'Elise (Streaming)',
                'description': 'Female voice, clear and friendly - Streaming version'
            },
            {
                'id': 'tara_async',
                'name': 'Tara (Non-Streaming)',
                'description': 'Female voice, warm and professional - Non-streaming backup'
            },
            {
                'id': 'elise_async',
                'name': 'Elise (Non-Streaming)', 
                'description': 'Female voice, clear and friendly - Non-streaming backup'
            }
        ]
        
        return jsonify({
            'voices': voices,
            'count': len(voices)
        })
        
    except Exception as e:
        logger.error(f"Error fetching voices: {str(e)}")
        return jsonify({'error': str(e)}), 500

def preprocess_text_for_word_protection(text):
    """Preprocess text to prevent TTS from cutting first/last words"""
    if not text or len(text.strip()) == 0:
        return text
    
    # Clean and normalize the text
    text = text.strip()
    
    # Add strategic breathing room at the beginning and end
    # The TTS model needs "warm-up" and "cool-down" to generate complete words
    
    # Method 1: Add invisible pauses using punctuation
    # Add a brief pause at the start with ellipsis
    text = "... " + text
    
    # Ensure proper sentence ending
    if not text.endswith(('.', '!', '?', '...')):
        text = text + "."
    
    # Add a trailing pause to ensure the last word is fully generated
    text = text + " ..."
    
    # Method 2: Add SSML-style pauses if the TTS supports them
    # Some TTS models respect timing markers
    text = f"<break time='100ms'/>{text}<break time='200ms'/>"
    
    logger.info(f"Word-protection preprocessing: '{text}'")
    return text

def enhance_text_for_complete_speech(text):
    """Enhanced text processing to ensure complete word generation"""
    if not text or len(text.strip()) == 0:
        return text
    
    original_text = text.strip()
    
    # Step 1: Ensure proper sentence structure
    # Add capital letter at start if missing
    if original_text and not original_text[0].isupper():
        original_text = original_text[0].upper() + original_text[1:]
    
    # Step 2: Add strategic padding for word preservation
    # Use natural-sounding padding that won't interfere with meaning
    
    # For very short text (single words), add extra context
    word_count = len(original_text.split())
    if word_count <= 2:
        # For single words or very short phrases, add natural context
        enhanced_text = f"Here's what I want to say: {original_text}. That's all."
    elif word_count <= 5:
        # For short phrases, add natural breathing room
        enhanced_text = f"Let me tell you: {original_text}. Thank you."
    else:
        # For longer text, just add padding at start/end
        enhanced_text = f"Here we go: {original_text}. That's complete."
    
    logger.info(f"Enhanced text for complete speech: '{original_text}' -> '{enhanced_text}'")
    return enhanced_text

def optimize_text_for_tts_model(text, model_type='baseten'):
    """ENHANCED: Advanced text optimization to prevent word cutting and improve synthesis quality"""
    if not text or len(text.strip()) == 0:
        return text

    text = text.strip()

    # ENHANCED: Advanced text preprocessing for better TTS synthesis

    # Step 1: Normalize text structure
    # Ensure proper capitalization
    if text and not text[0].isupper():
        text = text[0].upper() + text[1:]

    # Step 2: Add strategic pauses and breathing room
    word_count = len(text.split())

    if word_count <= 2:
        # Very short text - add natural context to prevent cutting
        optimized_text = f"Let me say this: {text}. That's it."
    elif word_count <= 5:
        # Short phrases - add minimal natural padding
        optimized_text = f"Here's what I mean: {text}. Got it."
    elif word_count <= 10:
        # Medium text - add subtle breathing room
        optimized_text = f"Okay, so {text}. That's what I wanted to say."
    else:
        # Longer text - minimal intervention, just ensure proper structure
        optimized_text = text

    # Step 3: Ensure proper sentence structure and punctuation
    if not optimized_text.endswith(('.', '!', '?')):
        optimized_text += '.'

    # Step 4: Add strategic pauses for better synthesis
    # Replace certain patterns with pause-friendly alternatives
    optimized_text = optimized_text.replace(' and ', ', and ')
    optimized_text = optimized_text.replace(' but ', ', but ')
    optimized_text = optimized_text.replace(' or ', ', or ')
    optimized_text = optimized_text.replace(' so ', ', so ')

    # Step 5: Ensure proper spacing after punctuation
    optimized_text = optimized_text.replace('.', '. ')
    optimized_text = optimized_text.replace(',', ', ')
    optimized_text = optimized_text.replace('!', '! ')
    optimized_text = optimized_text.replace('?', '? ')
    optimized_text = optimized_text.replace(';', '; ')
    optimized_text = optimized_text.replace(':', ': ')

    # Step 6: Clean up multiple spaces
    while '  ' in optimized_text:
        optimized_text = optimized_text.replace('  ', ' ')

    # Step 7: Final cleanup
    optimized_text = optimized_text.strip()

    # Step 8: Add subtle start/end padding for synthesis stability
    # This helps prevent the TTS model from cutting the first and last words
    optimized_text = f"... {optimized_text} ..."

    logger.info(f"✅ Enhanced text optimization: '{text}' -> '{optimized_text}'")
    return optimized_text

def subtle_word_protection(text):
    """Subtle text protection that sounds more natural while preventing word cutting"""
    if not text or len(text.strip()) == 0:
        return text
    
    text = text.strip()
    word_count = len(text.split())
    
    # Method 1: For very short text (1-2 words), minimal padding
    if word_count <= 2:
        # Just add subtle pauses without changing meaning
        protected_text = f".. {text} .."
        logger.info(f"Subtle protection (short): '{text}' -> '{protected_text}'")
        return protected_text
    
    # Method 2: For medium text (3-8 words), add natural breathing
    elif word_count <= 8:
        # Add natural pause markers
        protected_text = f"Um, {text}. Yeah."
        logger.info(f"Subtle protection (medium): '{text}' -> '{protected_text}'")
        return protected_text
    
    # Method 3: For longer text, minimal intervention
    else:
        # Just ensure proper punctuation and add tiny pauses
        if not text.endswith(('.', '!', '?')):
            text += '.'
        protected_text = f"So, {text} Right."
        logger.info(f"Subtle protection (long): '{text}' -> '{protected_text}'")
        return protected_text

def smart_word_protection(text, protection_level='balanced'):
    """Smart word protection with different levels of intervention"""
    if not text or len(text.strip()) == 0:
        return text
    
    text = text.strip()
    
    if protection_level == 'minimal':
        # Just add punctuation and tiny pauses
        if not text.endswith(('.', '!', '?')):
            text += '.'
        return f". {text} ."
    
    elif protection_level == 'balanced':
        # Use the optimized approach (current default)
        return optimize_text_for_tts_model(text)
    
    elif protection_level == 'aggressive':
        # Maximum protection for problematic cases
        return enhance_text_for_complete_speech(text)
    
    elif protection_level == 'subtle':
        # Natural-sounding protection
        return subtle_word_protection(text)
    
    else:
        # Default to balanced
        return optimize_text_for_tts_model(text) 