#!/usr/bin/env python3
"""
Optimized LiveKit Agent Template with Fixed Orpheus TTS
This template uses the optimized TTS implementation to fix audio artifacts and word cutting.
"""

import asyncio
import logging
import os
from typing import Annotated

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.plugins import openai, deepgram, silero

# Import our optimized TTS
from orpheus_optimized_tts import OptimizedOrpheusTTS

# Configure logging
logger = logging.getLogger("optimized-agent")

# System prompt for the agent
_system_prompt = """You are a helpful and friendly AI assistant. Keep your responses concise and natural for voice conversation. You have a warm personality and speak in a conversational tone."""

# User identity (will be replaced by dynamic agent system)
user_identity = "OptimizedAgent"

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the optimized voice agent."""
    
    # Initialize components with optimized settings
    initial_ctx = llm.ChatContext().append(
        role="system",
        text=_system_prompt,
    )
    
    # Use optimized Orpheus TTS with artifact fixes
    tts_instance = OptimizedOrpheusTTS(
        voice="tara",  # Can be "tara" or "elise"
        enable_resampling=True,  # Fix sample rate issues
        add_silence_padding=True,  # Prevent word cutting
    )
    
    # Create voice assistant with optimized components
    assistant = VoiceAssistant(
        vad=silero.VAD.load(),  # Voice activity detection
        stt=deepgram.STT(
            model="nova-2",
            language="en",
        ),
        llm=openai.LLM(
            model="gpt-4o-mini",
            temperature=0.7,
        ),
        tts=tts_instance,  # Use our optimized TTS
        chat_ctx=initial_ctx,
    )
    
    # Configure assistant behavior
    assistant.start(ctx.room)
    
    # Greet the user when they join
    await assistant.say("Hello! I'm your AI assistant. How can I help you today?", allow_interruptions=True)
    
    logger.info(f"🤖 {user_identity} started successfully with optimized TTS")

def prewarm(proc):
    """Prewarm function to load models before handling requests."""
    proc.userdata["vad"] = silero.VAD.load()

if __name__ == "__main__":
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        )
    )
