#!/usr/bin/env python3
"""
Quick test to verify the session fix for TTS
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_session_fix():
    """Test that the session fix resolves the executor shutdown issue"""
    
    print("🔧 Testing Session Fix for TTS")
    print("=" * 40)
    
    try:
        # Import our TTS
        from orpheus.tts import OrpheusTTS, Voice
        
        print("✅ TTS imports successful")
        
        # Create TTS instance
        tts = OrpheusTTS(
            voice=Voice(id="tara", name="Tara"),
            chunk_size_bytes=4096,
        )
        
        print("✅ TTS instance created")
        
        # Test simple synthesis
        text = "Hello! This is a test of the session fix."
        print(f"📝 Testing synthesis: '{text}'")
        
        # Create synthesis stream
        synthesis_stream = tts.synthesize(text)
        
        print("✅ Synthesis stream created")
        
        # Try to get some audio
        frame_count = 0
        async for audio_event in synthesis_stream:
            if hasattr(audio_event, 'frame') and audio_event.frame:
                frame_count += 1
                if frame_count >= 3:  # Get a few frames then stop
                    break
        
        if frame_count > 0:
            print(f"✅ SUCCESS: Received {frame_count} audio frames")
            print("🎉 Session fix is working - no executor shutdown!")
        else:
            print("❌ No audio frames received - issue may persist")
        
        # Clean up
        await tts.aclose()
        print("✅ TTS cleanup completed")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error")

async def test_streaming_fix():
    """Test streaming TTS with session fix"""
    
    print("\n🌊 Testing Streaming TTS Session Fix")
    print("=" * 40)
    
    try:
        from orpheus.tts import OrpheusTTS, Voice
        
        # Create TTS instance
        tts = OrpheusTTS(
            voice=Voice(id="tara", name="Tara"),
            chunk_size_bytes=4096,
        )
        
        # Create streaming session
        stream = tts.stream()
        
        print("✅ Streaming session created")
        
        # Send some text
        stream.push_text("Hello, this is a streaming test. ")
        stream.push_text("Testing session fix. ")
        stream.end_input()
        
        print("✅ Text sent to streaming session")
        
        # Wait a moment for processing
        await asyncio.sleep(3)
        
        # Close stream
        await stream.close()
        
        print("✅ Streaming test completed")
        
        # Clean up
        await tts.aclose()
        
    except Exception as e:
        print(f"❌ Streaming test failed: {e}")
        logger.exception("Streaming test error")

def main():
    """Main test function"""
    print("🚀 Testing TTS Session Fixes")
    print("This will verify that the 'Executor shutdown' error is resolved")
    print()
    
    # Run async tests
    asyncio.run(test_session_fix())
    asyncio.run(test_streaming_fix())
    
    print("\n🎯 Test Summary")
    print("=" * 40)
    print("If tests pass without 'Executor shutdown' errors,")
    print("the session fix is working correctly!")
    print()
    print("💡 The fix ensures fresh HTTP sessions are created")
    print("   for each synthesis request to avoid shutdown issues.")

if __name__ == "__main__":
    main()
