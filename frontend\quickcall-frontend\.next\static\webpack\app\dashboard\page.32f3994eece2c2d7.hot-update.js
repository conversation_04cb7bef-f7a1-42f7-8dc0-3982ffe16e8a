"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/WebCallingInterface.tsx":
/*!************************************************!*\
  !*** ./src/components/WebCallingInterface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebCallingInterface: () => (/* binding */ WebCallingInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _NoAgentNotification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NoAgentNotification */ \"(app-pages-browser)/./src/components/NoAgentNotification.tsx\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/contexts-Cm1aSBTs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/hooks-OJtwh4jO.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/prefabs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @livekit/components-react */ \"(app-pages-browser)/./node_modules/@livekit/components-react/dist/components-BeK2vIib.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Mic,Phone,PhoneOff,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* __next_internal_client_entry_do_not_use__ WebCallingInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n// API URL configuration\nconst API_URL = \"http://192.168.1.107:9090\" || 0;\n\n\n\n\n\n\nfunction WebCallingInterface(param) {\n    let { agentId, agentName, userId, onEndCall } = param;\n    _s();\n    const [room] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room());\n    const [isSpawningWorker, setIsSpawningWorker] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [workerError, setWorkerError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const onConnectButtonClicked = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"WebCallingInterface.useCallback[onConnectButtonClicked]\": async ()=>{\n            try {\n                setIsSpawningWorker(true);\n                setWorkerError(\"\");\n                // Step 1: Request microphone permissions with audio processing disabled\n                console.log(\"🎤 Requesting microphone permissions...\");\n                try {\n                    const stream = await navigator.mediaDevices.getUserMedia({\n                        audio: {\n                            echoCancellation: false,\n                            noiseSuppression: false,\n                            autoGainControl: false,\n                            sampleRate: 24000,\n                            channelCount: 1 // FIXED: Mono audio\n                        },\n                        video: false\n                    });\n                    // Stop the stream immediately - we just needed to check permissions\n                    stream.getTracks().forEach({\n                        \"WebCallingInterface.useCallback[onConnectButtonClicked]\": (track)=>track.stop()\n                    }[\"WebCallingInterface.useCallback[onConnectButtonClicked]\"]);\n                    console.log(\"✅ Microphone permissions granted with audio processing disabled\");\n                } catch (permissionError) {\n                    console.error(\"❌ Microphone permission denied:\", permissionError);\n                    throw new Error(\"Microphone permission is required for voice calling. Please allow microphone access and try again.\");\n                }\n                // Step 2: Get connection details first to generate unique room name\n                const url = new URL(\"\".concat(API_URL, \"/api/connection-details\"));\n                // Add agent ID and user ID to the connection details request for room isolation\n                if (agentId) {\n                    url.searchParams.set('agentId', agentId);\n                }\n                if (userId) {\n                    url.searchParams.set('userId', userId);\n                }\n                const response = await fetch(url.toString());\n                console.log('🔗 Connection details response status:', response.status);\n                if (!response.ok) {\n                    const errorData = await response.text();\n                    console.error('🔗 Connection details error:', errorData);\n                    throw new Error(\"Failed to get connection details: \".concat(response.status, \" - \").concat(errorData));\n                }\n                const connectionDetailsData = await response.json();\n                console.log('🔗 Connection details received:', connectionDetailsData);\n                // Step 3: Spawn LiveKit worker for the specific room (FIXED: now includes room_name)\n                if (agentId) {\n                    console.log(\"🤖 Spawning LiveKit worker for agent ID:\", agentId, \"in room:\", connectionDetailsData.roomName);\n                    const workerResponse = await fetch(\"\".concat(API_URL, \"/api/livekit/spawn\"), {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            agent_id: agentId,\n                            room_name: connectionDetailsData.roomName\n                        })\n                    });\n                    if (!workerResponse.ok) {\n                        const errorData = await workerResponse.json();\n                        throw new Error(errorData.error || 'LiveKit worker not available for this agent');\n                    }\n                    const workerResult = await workerResponse.json();\n                    console.log('✅ LiveKit worker spawned successfully:', workerResult);\n                }\n                // Step 4: Connect to LiveKit room and enable microphone with audio processing disabled\n                await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);\n                await room.localParticipant.setMicrophoneEnabled(true, {\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: false,\n                    sampleRate: 24000,\n                    channelCount: 1 // FIXED: Mono audio\n                });\n                console.log(\"🎉 Successfully connected to voice assistant\");\n            } catch (error) {\n                console.error('🚨 Error connecting to voice assistant:', error);\n                setWorkerError(error instanceof Error ? error.message : 'Failed to connect');\n            } finally{\n                setIsSpawningWorker(false);\n            }\n        }\n    }[\"WebCallingInterface.useCallback[onConnectButtonClicked]\"], [\n        room,\n        agentId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"WebCallingInterface.useEffect\": ()=>{\n            room.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.MediaDevicesError, onDeviceFailure);\n            return ({\n                \"WebCallingInterface.useEffect\": ()=>{\n                    room.off(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.MediaDevicesError, onDeviceFailure);\n                }\n            })[\"WebCallingInterface.useEffect\"];\n        }\n    }[\"WebCallingInterface.useEffect\"], [\n        room\n    ]);\n    function onDeviceFailure(error) {\n        console.error(\"Device failure:\", error);\n        // More specific error message based on the error type\n        let errorMessage = \"Microphone access error. \";\n        if (error.name === \"NotAllowedError\") {\n            errorMessage += \"Please allow microphone access in your browser settings and refresh the page.\";\n        } else if (error.name === \"NotFoundError\") {\n            errorMessage += \"No microphone found. Please connect a microphone and try again.\";\n        } else if (error.name === \"NotReadableError\") {\n            errorMessage += \"Microphone is being used by another application. Please close other apps using your microphone.\";\n        } else {\n            errorMessage += \"Please check your microphone settings and refresh the page.\";\n        }\n        setWorkerError(errorMessage);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-white flex flex-col overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_4__.R.Provider, {\n            value: room,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleVoiceAssistant, {\n                onConnectButtonClicked: onConnectButtonClicked,\n                isSpawningWorker: isSpawningWorker,\n                workerError: workerError,\n                agentName: agentName,\n                onEndCall: onEndCall\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(WebCallingInterface, \"Llct4kKfh/UAd25gIoT89xkY21k=\");\n_c = WebCallingInterface;\nfunction SimpleVoiceAssistant(props) {\n    _s1();\n    const { state: agentState } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.V)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full min-h-0 flex flex-col\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n            mode: \"wait\",\n            children: agentState === \"disconnected\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"flex flex-col items-center justify-center flex-1 p-3 sm:p-4 lg:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4 sm:mb-6 lg:mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm sm:text-lg lg:text-xl font-semibold text-gray-900 mb-1 sm:mb-2\",\n                                children: props.agentName\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm text-gray-600 mb-2\",\n                                children: \"Ready to start conversation\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-2 sm:p-3 mx-auto max-w-xs sm:max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-1 sm:gap-2 text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs sm:text-sm\",\n                                            children: \"Microphone access required\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this),\n                    props.workerError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-2 sm:p-3 mb-3 sm:mb-4 w-full max-w-xs sm:max-w-md text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 text-xs sm:text-sm\",\n                            children: props.workerError\n                        }, void 0, false, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"flex items-center justify-center gap-2 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 lg:py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm lg:text-base w-full max-w-xs sm:max-w-sm\",\n                        onClick: ()=>props.onConnectButtonClicked(),\n                        disabled: props.isSpawningWorker,\n                        children: props.isSpawningWorker ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden xs:inline sm:hidden lg:inline\",\n                                    children: \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"xs:hidden sm:inline lg:hidden\",\n                                    children: \"Connecting\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden xs:inline sm:hidden lg:inline\",\n                                    children: \"Start Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"xs:hidden sm:inline lg:hidden\",\n                                    children: \"Call\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"disconnected\", true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 192,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                exit: {\n                    opacity: 0\n                },\n                className: \"flex flex-col w-full h-full min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-2 sm:p-3 lg:p-4 bg-white border-b border-gray-200 flex-shrink-0 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 text-xs sm:text-sm lg:text-base truncate\",\n                                                children: props.agentName\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-600 hidden sm:inline\",\n                                                        children: \"Connected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-2 sm:mx-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveAgentStateIndicator, {\n                                    state: agentState\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 sm:gap-2 flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block [&_.lk-button]:bg-gray-50 [&_.lk-button]:hover:bg-gray-100 [&_.lk-button]:border-gray-200 [&_.lk-button]:text-gray-700 [&_.lk-button]:shadow-sm [&_.lk-button]:w-7 [&_.lk-button]:h-7 [&_.lk-button]:rounded-md [&_.lk-button]:transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_11__.VoiceAssistantControlBar, {\n                                            controls: {\n                                                leave: false\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.D, {\n                                        className: \"bg-red-500 hover:bg-red-600 text-white rounded-md w-7 h-7 sm:w-8 sm:h-8 shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center\",\n                                        onClick: props.onEndCall,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 bg-white flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 sm:w-10 sm:h-10 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm sm:text-base font-medium text-gray-900 mb-2\",\n                                    children: [\n                                        \"Connected to \",\n                                        props.agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs sm:text-sm text-gray-600\",\n                                    children: \"Voice conversation is active\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.R, {}, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoAgentNotification__WEBPACK_IMPORTED_MODULE_1__.NoAgentNotification, {\n                        state: agentState\n                    }, void 0, false, {\n                        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"connected\", true, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 247,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s1(SimpleVoiceAssistant, \"XOml166M2vIO8FJctr0RSKfKDkM=\", false, function() {\n    return [\n        _livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.V\n    ];\n});\n_c1 = SimpleVoiceAssistant;\n// Responsive Agent State Indicator\nfunction ResponsiveAgentStateIndicator(param) {\n    let { state } = param;\n    const getStateInfo = ()=>{\n        switch(state){\n            case \"listening\":\n                return {\n                    text: \"Listening\",\n                    shortText: \"Mic\",\n                    color: \"bg-blue-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n            case \"thinking\":\n                return {\n                    text: \"Thinking\",\n                    shortText: \"AI\",\n                    color: \"bg-yellow-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            case \"speaking\":\n                return {\n                    text: \"Speaking\",\n                    shortText: \"Talk\",\n                    color: \"bg-green-500\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                };\n            default:\n                return {\n                    text: \"Ready\",\n                    shortText: \"Ready\",\n                    color: \"bg-gray-400\",\n                    icon: _barrel_optimize_names_Loader2_Mic_Phone_PhoneOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n        }\n    };\n    const stateInfo = getStateInfo();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-1 sm:gap-2 px-1.5 sm:px-2 py-1 bg-gray-50 border border-gray-200 rounded-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full \".concat(stateInfo.color, \" \").concat(state === 'thinking' ? 'animate-pulse' : '')\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-600 hidden sm:inline\",\n                children: stateInfo.text\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-600 sm:hidden\",\n                children: stateInfo.shortText\n            }, void 0, false, {\n                fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\quickcall\\\\frontend\\\\quickcall-frontend\\\\src\\\\components\\\\WebCallingInterface.tsx\",\n        lineNumber: 333,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ResponsiveAgentStateIndicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WebCallingInterface\");\n$RefreshReg$(_c1, \"SimpleVoiceAssistant\");\n$RefreshReg$(_c2, \"ResponsiveAgentStateIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebCallingInterface.tsx\n"));

/***/ })

});